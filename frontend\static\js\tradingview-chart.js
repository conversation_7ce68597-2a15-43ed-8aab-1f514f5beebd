/**
 * Professional TradingView Chart Implementation with Infinite History
 * Uses official TradingView Lightweight Charts library with real Binance data
 * Implements infinite scrolling for historical data loading
 */

/**
 * Data Feed Class for managing historical data with infinite loading
 */
class BinanceDataFeed {
    constructor(symbol, interval) {
        this.symbol = symbol;
        this.interval = interval;
        this.data = [];
        this.earliestTime = null;
        this.isLoading = false;
        this.hasMoreData = true;
    }

    async loadInitialData(limit = 500) {
        try {
            const response = await fetch(`/api/v1/ohlcv/fetch?symbol=${this.symbol}&timeframe=${this.interval}&exchange=binance&limit=${limit}`);
            const result = await response.json();

            if (result.success && result.data && result.data.ohlcv) {
                this.data = this.processOHLCVData(result.data.ohlcv);
                if (this.data.length > 0) {
                    this.earliestTime = this.data[0].time;
                }
                return this.data;
            } else {
                throw new Error('Failed to load initial data');
            }
        } catch (error) {
            console.error('Error loading initial data:', error);
            return this.generateSampleData(limit);
        }
    }

    async loadMoreData(numberOfBars = 500) {
        if (this.isLoading || !this.hasMoreData) {
            return this.data;
        }

        this.isLoading = true;

        try {
            // Calculate the end time for the historical data request
            const endTime = new Date(this.earliestTime * 1000);
            endTime.setMinutes(endTime.getMinutes() - this.getIntervalMinutes());

            const response = await fetch(
                `/api/v1/ohlcv/fetch?symbol=${this.symbol}&timeframe=${this.interval}&exchange=binance&limit=${numberOfBars}&end_time=${endTime.toISOString()}`
            );
            const result = await response.json();

            if (result.success && result.data && result.data.ohlcv && result.data.ohlcv.length > 0) {
                const newData = this.processOHLCVData(result.data.ohlcv);

                // Prepend new data to existing data
                this.data = [...newData, ...this.data];
                this.earliestTime = newData[0].time;

                // Check if we've reached the limit of available data
                if (newData.length < numberOfBars) {
                    this.hasMoreData = false;
                }
            } else {
                // If API fails, generate sample historical data
                const newData = this.generateHistoricalSampleData(numberOfBars);
                this.data = [...newData, ...this.data];
                this.earliestTime = newData[0].time;
            }
        } catch (error) {
            console.error('Error loading more data:', error);
            // Generate sample data as fallback
            const newData = this.generateHistoricalSampleData(numberOfBars);
            this.data = [...newData, ...this.data];
            this.earliestTime = newData[0].time;
        } finally {
            this.isLoading = false;
        }

        return this.data;
    }

    processOHLCVData(ohlcvData) {
        return ohlcvData
            .map(item => {
                try {
                    let timestamp;
                    if (typeof item.timestamp === 'number') {
                        timestamp = item.timestamp > 1000000000000 ?
                            Math.floor(item.timestamp / 1000) :
                            Math.floor(item.timestamp);
                    } else {
                        timestamp = Math.floor(new Date(item.timestamp).getTime() / 1000);
                    }

                    return {
                        time: timestamp,
                        open: parseFloat(item.open),
                        high: parseFloat(item.high),
                        low: parseFloat(item.low),
                        close: parseFloat(item.close),
                        volume: parseFloat(item.volume)
                    };
                } catch (error) {
                    console.warn('Error processing OHLCV item:', item, error);
                    return null;
                }
            })
            .filter(item => item !== null)
            .sort((a, b) => a.time - b.time);
    }

    generateSampleData(numberOfBars) {
        const data = [];
        const intervalMinutes = this.getIntervalMinutes();
        const endTime = Math.floor(Date.now() / 1000);
        const startTime = endTime - (numberOfBars * intervalMinutes * 60);

        let basePrice = this.symbol.includes('BTC') ? 45000 :
                       this.symbol.includes('ETH') ? 3000 : 100;

        for (let i = 0; i < numberOfBars; i++) {
            const time = startTime + (i * intervalMinutes * 60);
            const volatility = basePrice * 0.002;

            const open = i === 0 ? basePrice : data[i - 1].close;
            const change = (Math.random() - 0.5) * volatility * 2;
            const close = open + change;
            const high = Math.max(open, close) + Math.random() * volatility;
            const low = Math.min(open, close) - Math.random() * volatility;
            const volume = Math.random() * 1000000 + 100000;

            data.push({
                time: time,
                open: parseFloat(open.toFixed(2)),
                high: parseFloat(high.toFixed(2)),
                low: parseFloat(low.toFixed(2)),
                close: parseFloat(close.toFixed(2)),
                volume: parseFloat(volume.toFixed(0))
            });
        }

        this.earliestTime = data[0].time;
        return data;
    }

    generateHistoricalSampleData(numberOfBars) {
        const data = [];
        const intervalMinutes = this.getIntervalMinutes();
        const startTime = this.earliestTime - (numberOfBars * intervalMinutes * 60);

        // Get the last known price to continue the trend
        const lastPrice = this.data.length > 0 ? this.data[0].open : 45000;

        for (let i = 0; i < numberOfBars; i++) {
            const time = startTime + (i * intervalMinutes * 60);
            const volatility = lastPrice * 0.002;

            const open = i === 0 ? lastPrice * (0.98 + Math.random() * 0.04) : data[i - 1].close;
            const change = (Math.random() - 0.5) * volatility * 2;
            const close = open + change;
            const high = Math.max(open, close) + Math.random() * volatility;
            const low = Math.min(open, close) - Math.random() * volatility;
            const volume = Math.random() * 1000000 + 100000;

            data.push({
                time: time,
                open: parseFloat(open.toFixed(2)),
                high: parseFloat(high.toFixed(2)),
                low: parseFloat(low.toFixed(2)),
                close: parseFloat(close.toFixed(2)),
                volume: parseFloat(volume.toFixed(0))
            });
        }

        return data;
    }

    getIntervalMinutes() {
        const intervalMap = {
            '1m': 1, '3m': 3, '5m': 5, '15m': 15, '30m': 30,
            '1h': 60, '2h': 120, '4h': 240, '6h': 360, '8h': 480, '12h': 720,
            '1d': 1440, '3d': 4320, '1w': 10080, '1M': 43200
        };
        return intervalMap[this.interval] || 1;
    }

    getData() {
        return this.data;
    }

    getVolumeData() {
        return this.data.map(item => ({
            time: item.time,
            value: item.volume,
            color: item.close >= item.open ? '#26a69a80' : '#ef535080'
        }));
    }
}

class TradingViewChart {
    constructor(containerId, options = {}) {
        this.containerId = containerId;
        this.container = document.getElementById(containerId);
        this.chart = null;
        this.candlestickSeries = null;
        this.volumeSeries = null;
        this.websocket = null;
        this.isConnected = false;
        this.dataFeed = null;

        // Default configuration
        this.config = {
            symbol: 'BTCUSDT',
            interval: '1m',
            theme: 'dark',
            autoResize: true,
            enableWebSocket: true,
            enableInfiniteHistory: true,
            ...options
        };

        // Chart data
        this.currentData = [];
        this.lastUpdateTime = 0;
        this.lastErrorTime = 0;
        this.isLoadingHistory = false;

        // Initialize custom price lines array
        this.customPriceLines = [];

        // Initialize support and resistance tracking
        this.supportResistanceLines = {
            support: null,
            resistance: null
        };
        this.analysisWindow = 50; // Number of candles to analyze
        this.supportResistanceEnabled = true; // Enable by default

        this.init();
    }
    
    init() {
        if (!this.container) {
            console.error(`Container ${this.containerId} not found`);
            return;
        }

        this.createChart();
        this.setupEventListeners();
        this.initializeDataFeed();

        if (this.config.enableInfiniteHistory) {
            this.setupInfiniteHistory();
        }

        if (this.config.enableWebSocket) {
            this.connectWebSocket();
        }
    }

    initializeDataFeed() {
        this.dataFeed = new BinanceDataFeed(this.config.symbol, this.config.interval);
    }

    setupInfiniteHistory() {
        if (!this.chart) return;

        this.chart.timeScale().subscribeVisibleLogicalRangeChange(logicalRange => {
            if (logicalRange && logicalRange.from < 10 && !this.isLoadingHistory) {
                this.loadMoreHistoricalData(logicalRange.from);
            }
        });
    }

    async loadMoreHistoricalData(currentFrom) {
        if (this.isLoadingHistory || !this.dataFeed || !this.dataFeed.hasMoreData) {
            return;
        }

        this.isLoadingHistory = true;
        this.updateStatus('Loading historical data...');

        try {
            const numberBarsToLoad = Math.max(50, 50 - currentFrom);
            console.log(`Loading ${numberBarsToLoad} more historical bars...`);

            const allData = await this.dataFeed.loadMoreData(numberBarsToLoad);

            // Add a small delay to show loading state
            setTimeout(() => {
                this.candlestickSeries.setData(allData);
                this.volumeSeries.setData(this.dataFeed.getVolumeData());

                // Update currentData with all loaded data
                this.currentData = allData;

                // Update support and resistance levels with new data
                this.updateSupportResistanceLevels();

                this.updateStatus(`Loaded ${allData.length} total candles for ${this.config.symbol}`);
                this.isLoadingHistory = false;
            }, 100);

        } catch (error) {
            console.error('Error loading historical data:', error);
            this.updateStatus('Error loading historical data', 'error');
            this.isLoadingHistory = false;
        }
    }
    
    createChart() {
        // Chart options based on TradingView style
        const chartOptions = {
            width: this.container.clientWidth,
            height: this.container.clientHeight || 600,
            layout: {
                backgroundColor: this.config.theme === 'dark' ? '#131722' : '#FFFFFF',
                textColor: this.config.theme === 'dark' ? '#d1d4dc' : '#191919',
                fontSize: 12,
                fontFamily: 'Trebuchet MS, sans-serif',
            },
            grid: {
                vertLines: {
                    color: this.config.theme === 'dark' ? '#363c4e' : '#e1e3e6',
                    style: 1,
                    visible: true,
                },
                horzLines: {
                    color: this.config.theme === 'dark' ? '#363c4e' : '#e1e3e6',
                    style: 1,
                    visible: true,
                },
            },
            crosshair: {
                mode: LightweightCharts.CrosshairMode.Normal,
                vertLine: {
                    color: this.config.theme === 'dark' ? '#758696' : '#9598a1',
                    width: 1,
                    style: 3,
                    visible: true,
                    labelVisible: true,
                },
                horzLine: {
                    color: this.config.theme === 'dark' ? '#758696' : '#9598a1',
                    width: 1,
                    style: 3,
                    visible: true,
                    labelVisible: true,
                },
            },
            rightPriceScale: {
                borderColor: this.config.theme === 'dark' ? '#485c7b' : '#d0d3d7',
                textColor: this.config.theme === 'dark' ? '#b2b5be' : '#191919',
                entireTextOnly: false,
                visible: true,
                scaleMargins: {
                    top: 0.1,
                    bottom: 0.1,
                },
                // Configure price scale for 250-point intervals
                mode: 0, // Normal price scale mode
                autoScale: true,
                invertScale: false,
                alignLabels: true,
                borderVisible: true,
                ticksVisible: true,
                minimumWidth: 80,
                // Set minimum tick step to 250 points
                tickMarkFormatter: (price) => {
                    // Format price with proper intervals
                    if (price >= 100000) {
                        return `$${(price / 1000).toFixed(0)}K`;
                    } else if (price >= 1000) {
                        return `$${(price / 1000).toFixed(1)}K`;
                    } else {
                        return `$${price.toFixed(0)}`;
                    }
                },
            },
            timeScale: {
                borderColor: this.config.theme === 'dark' ? '#485c7b' : '#d0d3d7',
                textColor: this.config.theme === 'dark' ? '#b2b5be' : '#191919',
                timeVisible: true,
                secondsVisible: false,
                rightOffset: 12,
                barSpacing: 6,
                fixLeftEdge: false,
                lockVisibleTimeRangeOnResize: true,
                rightBarStaysOnScroll: true,
                borderVisible: true,
                visible: true,
                // Configure time scale for 4-hour interval display
                ticksVisible: true,
                tickMarkFormatter: (time, tickMarkType, locale) => {
                    const date = new Date(time * 1000);

                    if (tickMarkType === 0) { // Year
                        return date.getFullYear().toString();
                    } else if (tickMarkType === 1) { // Month
                        return date.toLocaleDateString('en-US', { month: 'short', year: 'numeric' });
                    } else if (tickMarkType === 2) { // DayOfMonth
                        return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
                    } else { // Time - show in 4-hour format
                        const hours = date.getHours();
                        // Show ticks every 4 hours (0, 4, 8, 12, 16, 20)
                        if (hours % 4 === 0) {
                            return date.toLocaleTimeString('en-US', {
                                hour: '2-digit',
                                minute: '2-digit',
                                hour12: false
                            });
                        }
                        return ''; // Hide non-4-hour marks
                    }
                },
            },
            handleScroll: {
                mouseWheel: true,
                pressedMouseMove: true,
                horzTouchDrag: true,
                vertTouchDrag: true,
            },
            handleScale: {
                axisPressedMouseMove: true,
                mouseWheel: true,
                pinch: true,
            },
        };
        
        // Create chart
        this.chart = LightweightCharts.createChart(this.container, chartOptions);

        // Create candlestick series
        this.candlestickSeries = this.chart.addCandlestickSeries({
            upColor: '#26a69a',
            downColor: '#ef5350',
            borderVisible: false,
            wickUpColor: '#26a69a',
            wickDownColor: '#ef5350',
            priceFormat: {
                type: 'price',
                precision: 2,
                minMove: 0.01,
            },
        });
        
        // Create volume series
        this.volumeSeries = this.chart.addHistogramSeries({
            color: '#26a69a',
            priceFormat: {
                type: 'volume',
            },
            priceScaleId: '',
            scaleMargins: {
                top: 0.8,
                bottom: 0,
            },
        });
        
        // Setup crosshair move handler with error protection
        this.chart.subscribeCrosshairMove((param) => {
            try {
                this.handleCrosshairMove(param);
                this.updatePriceDisplay(param);
                this.showOHLCVTooltip(param);
            } catch (error) {
                // Silently handle crosshair errors to prevent console spam
                console.debug('Crosshair error:', error);
            }
        });



        // Create OHLCV tooltip element
        this.createOHLCVTooltip();

        // Apply default chart configurations
        this.applyChartConfiguration();

        console.log('TradingView chart created successfully');
        console.log('OHLCV tooltip created:', !!this.ohlcvTooltip);
        console.log('Price display created:', !!this.priceDisplay);
    }

    createOHLCVTooltip() {
        // Ensure container has relative positioning
        if (this.container) {
            this.container.style.position = 'relative';
        }

        // Create enhanced tooltip container
        this.ohlcvTooltip = document.createElement('div');
        this.ohlcvTooltip.className = 'ohlcv-tooltip';
        this.ohlcvTooltip.style.cssText = `
            position: absolute;
            background: rgba(19, 23, 34, 0.98);
            color: white;
            padding: 16px;
            border-radius: 8px;
            font-family: 'Segoe UI', sans-serif;
            font-size: 12px;
            line-height: 1.5;
            pointer-events: none;
            z-index: 10000;
            display: none;
            min-width: 280px;
            max-width: 320px;
            border: 2px solid #26a69a;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.8);
        `;

        console.log('✅ Enhanced tooltip container created');

        // Create enhanced price display element (crosshair price)
        this.priceDisplay = document.createElement('div');
        this.priceDisplay.className = 'price-display';
        this.priceDisplay.style.cssText = `
            position: absolute;
            right: 8px;
            background: linear-gradient(135deg, rgba(255, 215, 0, 0.9), rgba(255, 193, 7, 0.9));
            color: #000;
            padding: 6px 10px;
            border-radius: 6px;
            font-family: 'SF Mono', 'Monaco', 'Cascadia Code', 'Roboto Mono', monospace;
            font-size: 12px;
            font-weight: 600;
            pointer-events: none;
            z-index: 1001;
            display: none;
            border: 1px solid rgba(255, 215, 0, 0.3);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
            letter-spacing: 0.5px;
        `;

        // Append to container
        this.container.appendChild(this.ohlcvTooltip);
        this.container.appendChild(this.priceDisplay);

        console.log('Enhanced OHLCV tooltip elements created and appended to container');
    }

    applyChartConfiguration() {
        if (!this.chart) return;

        console.log('Applying chart configuration: 4-hour time intervals, 250-point price intervals');

        // Configure price scale for 250-point intervals
        this.chart.priceScale('right').applyOptions({
            scaleMargins: {
                top: 0.1,
                bottom: 0.1,
            },
            mode: 0, // Normal price scale mode
            autoScale: true,
            invertScale: false,
            alignLabels: true,
            borderVisible: true,
            ticksVisible: true,
            minimumWidth: 80,
            // Configure tick intervals
            tickMarkFormatter: (price) => {
                // Round to nearest 250 for cleaner display
                const interval = 250;
                const rounded = Math.round(price / interval) * interval;

                // Format the price
                if (rounded >= 100000) {
                    return `$${(rounded / 1000).toFixed(0)}K`;
                } else if (rounded >= 1000) {
                    return `$${(rounded / 1000).toFixed(1)}K`;
                } else {
                    return `$${rounded.toFixed(0)}`;
                }
            },
        });

        // Configure time scale for 4-hour interval display
        this.chart.timeScale().applyOptions({
            // Set minimum bar spacing to help with time intervals
            barSpacing: 8,
            rightOffset: 12,
            fixLeftEdge: false,
            lockVisibleTimeRangeOnResize: true,
            rightBarStaysOnScroll: true,
            borderVisible: true,
            visible: true,
            ticksVisible: true,
            tickMarkFormatter: (time, tickMarkType, locale) => {
                const date = new Date(time * 1000);

                    if (tickMarkType === 0) { // Year
                        return date.getFullYear().toString();
                    } else if (tickMarkType === 1) { // Month
                        return date.toLocaleDateString('en-US', { month: 'short', year: 'numeric' });
                    } else if (tickMarkType === 2) { // DayOfMonth
                        return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
                    } else { // Time
                        const hours = date.getHours();

                        // Show ticks every 4 hours (0, 4, 8, 12, 16, 20)
                        if (hours % 4 === 0) {
                            return `${hours.toString().padStart(2, '0')}:00`;
                        }
                        return ''; // Hide non-4-hour marks
                    }
                },
            }
        });

        console.log('Chart configuration applied successfully');

        // Test the formatters with sample values
        this.testFormatters();
    }

    /**
     * Test the tick formatters with sample values
     */
    testFormatters() {
        console.log('Testing formatters:');

        // Test price formatter - now always rounds to 250 intervals
        const testPrices = [118093, 118000, 118250, 118500, 119000, 119250];
        testPrices.forEach(price => {
            const interval = 250;
            const rounded = Math.round(price / interval) * interval;
            console.log(`Price ${price} -> $${rounded >= 1000 ? (rounded / 1000).toFixed(1) + 'K' : rounded.toFixed(0)}`);
        });

        // Test time formatter - now always rounds to 6-hour intervals
        const testTimes = [
            new Date('2024-07-29T00:00:00'),
            new Date('2024-07-29T03:30:00'),
            new Date('2024-07-29T06:00:00'),
            new Date('2024-07-29T09:45:00'),
            new Date('2024-07-29T12:00:00'),
            new Date('2024-07-29T15:45:00'),
            new Date('2024-07-29T18:00:00'),
            new Date('2024-07-29T21:30:00')
        ];
        testTimes.forEach(date => {
            const hours = date.getHours();
            const roundedHours = Math.round(hours / 6) * 6;
            console.log(`Time ${hours}:00 -> ${roundedHours.toString().padStart(2, '0')}:00`);
        });
    }

    /**
     * Analyze the last N candlesticks and update support/resistance lines
     */
    updateSupportResistanceLevels() {
        if (!this.supportResistanceEnabled || !this.chart || !this.candlestickSeries || !this.currentData || this.currentData.length < this.analysisWindow) {
            return;
        }

        try {
            // Get the last N candles for analysis
            const recentCandles = this.currentData.slice(-this.analysisWindow);

            // Find highest high (resistance) and lowest low (support)
            let highestHigh = -Infinity;
            let lowestLow = Infinity;

            recentCandles.forEach(candle => {
                if (candle.high > highestHigh) {
                    highestHigh = candle.high;
                }
                if (candle.low < lowestLow) {
                    lowestLow = candle.low;
                }
            });

            console.log(`Support/Resistance Analysis (last ${this.analysisWindow} candles):`);
            console.log(`Resistance (Highest High): $${highestHigh.toFixed(2)}`);
            console.log(`Support (Lowest Low): $${lowestLow.toFixed(2)}`);

            // Remove existing support/resistance lines
            this.removeSupportResistanceLines();

            // Add new support line (lowest low)
            this.supportResistanceLines.support = this.candlestickSeries.createPriceLine({
                price: lowestLow,
                color: '#ff4444', // Red for support
                lineWidth: 2,
                lineStyle: LightweightCharts.LineStyle.Solid,
                axisLabelVisible: true,
                title: `Support: $${lowestLow >= 1000 ? (lowestLow / 1000).toFixed(2) + 'K' : lowestLow.toFixed(2)}`,
            });

            // Add new resistance line (highest high)
            this.supportResistanceLines.resistance = this.candlestickSeries.createPriceLine({
                price: highestHigh,
                color: '#44ff44', // Green for resistance
                lineWidth: 2,
                lineStyle: LightweightCharts.LineStyle.Solid,
                axisLabelVisible: true,
                title: `Resistance: $${highestHigh >= 1000 ? (highestHigh / 1000).toFixed(2) + 'K' : highestHigh.toFixed(2)}`,
            });

            console.log('Support and resistance lines updated successfully');

        } catch (error) {
            console.error('Error updating support/resistance levels:', error);
        }
    }

    /**
     * Remove existing support and resistance lines
     */
    removeSupportResistanceLines() {
        try {
            if (this.supportResistanceLines.support) {
                this.candlestickSeries.removePriceLine(this.supportResistanceLines.support);
                this.supportResistanceLines.support = null;
            }
            if (this.supportResistanceLines.resistance) {
                this.candlestickSeries.removePriceLine(this.supportResistanceLines.resistance);
                this.supportResistanceLines.resistance = null;
            }
        } catch (error) {
            console.debug('Error removing support/resistance lines:', error);
        }
    }

    /**
     * Update the currentData array with new candle data from WebSocket
     */
    updateCurrentDataWithNewCandle(newCandle) {
        if (!this.currentData) {
            this.currentData = [];
        }

        // Check if this is an update to the last candle or a new candle
        if (this.currentData.length > 0) {
            const lastCandle = this.currentData[this.currentData.length - 1];

            // If the timestamp matches the last candle, update it (same time period)
            if (lastCandle.time === newCandle.time) {
                this.currentData[this.currentData.length - 1] = { ...newCandle };
                console.debug('Updated existing candle at time:', newCandle.time);
            } else {
                // New candle, add it to the array
                this.currentData.push({ ...newCandle });
                console.debug('Added new candle at time:', newCandle.time);

                // Keep only the last 1000 candles to prevent memory issues
                if (this.currentData.length > 1000) {
                    this.currentData = this.currentData.slice(-1000);
                }
            }
        } else {
            // First candle
            this.currentData.push({ ...newCandle });
        }
    }

    /**
     * Set the number of candles to analyze for support/resistance levels
     * @param {number} windowSize - Number of recent candles to analyze (default: 50)
     */
    setSupportResistanceWindow(windowSize = 50) {
        if (windowSize < 10) {
            console.warn('Analysis window too small, minimum is 10 candles');
            windowSize = 10;
        }
        if (windowSize > 500) {
            console.warn('Analysis window too large, maximum is 500 candles');
            windowSize = 500;
        }

        this.analysisWindow = windowSize;
        console.log(`Support/Resistance analysis window set to ${windowSize} candles`);

        // Update levels with new window size
        this.updateSupportResistanceLevels();
    }

    /**
     * Enable or disable automatic support/resistance level detection
     * @param {boolean} enabled - Whether to enable automatic detection
     */
    enableSupportResistanceDetection(enabled = true) {
        this.supportResistanceEnabled = enabled;

        if (!enabled) {
            this.removeSupportResistanceLines();
            console.log('Support/Resistance detection disabled');
        } else {
            this.updateSupportResistanceLevels();
            console.log('Support/Resistance detection enabled');
        }
    }

    // Method to update chart theme
    updateTheme(themeOptions) {
        if (!this.chart) {
            console.warn('Chart not initialized, cannot update theme');
            return;
        }

        try {
            // Apply layout and grid options
            this.chart.applyOptions({
                layout: {
                    background: {
                        type: 'solid',
                        color: themeOptions.background
                    },
                    textColor: themeOptions.text,
                },
                grid: {
                    vertLines: {
                        color: themeOptions.grid,
                        style: 1,
                        visible: true,
                    },
                    horzLines: {
                        color: themeOptions.grid,
                        style: 1,
                        visible: true,
                    },
                },
                crosshair: {
                    mode: 0,
                    vertLine: {
                        color: themeOptions.crosshair,
                        width: 1,
                        style: 3,
                    },
                    horzLine: {
                        color: themeOptions.crosshair,
                        width: 1,
                        style: 3,
                    },
                },
                timeScale: {
                    borderColor: themeOptions.borderColor,
                    textColor: themeOptions.text,
                },
                rightPriceScale: {
                    borderColor: themeOptions.borderColor,
                    textColor: themeOptions.text,
                },
            });

            // Update candlestick colors
            if (this.candlestickSeries) {
                this.candlestickSeries.applyOptions({
                    upColor: themeOptions.upColor,
                    downColor: themeOptions.downColor,
                    borderUpColor: themeOptions.upColor,
                    borderDownColor: themeOptions.downColor,
                    wickUpColor: themeOptions.wickUpColor,
                    wickDownColor: themeOptions.wickDownColor,
                });
            }

            console.log('Chart theme updated successfully');
        } catch (error) {
            console.error('Error updating chart theme:', error);
        }
    }

    showOHLCVTooltip(param) {
        // Create tooltip if it doesn't exist
        if (!this.ohlcvTooltip) {
            this.createOHLCVTooltip();
        }

        if (!param.time || !param.seriesData) {
            if (this.ohlcvTooltip) {
                this.ohlcvTooltip.style.display = 'none';
            }
            if (this.priceDisplay) {
                this.priceDisplay.style.display = 'none';
            }
            return;
        }

        // Get candlestick data for the current time
        const candleData = param.seriesData.get(this.candlestickSeries);
        const volumeData = param.seriesData.get(this.volumeSeries);

        if (!candleData) {
            this.ohlcvTooltip.style.display = 'none';
            return;
        }

        // Format timestamp with more detail
        const date = new Date(param.time * 1000);
        const timestamp = date.toLocaleString('en-US', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit',
            hour12: false,
            timeZoneName: 'short'
        });

        // Unix timestamp for reference
        const unixTimestamp = param.time;

        // Format currency values with higher precision
        const formatPrice = (price) => {
            if (price === null || price === undefined) return 'N/A';
            return new Intl.NumberFormat('en-US', {
                style: 'currency',
                currency: 'USD',
                minimumFractionDigits: 2,
                maximumFractionDigits: 8
            }).format(price);
        };

        const formatVolume = (volume) => {
            if (volume === null || volume === undefined) return 'N/A';
            if (volume >= 1000000) {
                return (volume / 1000000).toFixed(2) + 'M';
            } else if (volume >= 1000) {
                return (volume / 1000).toFixed(2) + 'K';
            }
            return new Intl.NumberFormat('en-US', {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2
            }).format(volume);
        };

        // Calculate price change and percentage
        const priceChange = candleData.close - candleData.open;
        const priceChangePercent = ((priceChange / candleData.open) * 100);
        const changeColor = priceChange >= 0 ? '#26a69a' : '#ef5350';
        const changeSymbol = priceChange >= 0 ? '+' : '';

        // Get current price at cursor position
        const currentPrice = this.chart.coordinateToPrice(param.point?.y || 0);
        const formattedCurrentPrice = currentPrice ? formatPrice(currentPrice) : 'N/A';

        // Build enhanced tooltip content
        let tooltipContent = `
            <div style="font-weight: bold; margin-bottom: 10px; color: #26a69a; font-size: 14px;">
                📊 OHLCV Data
            </div>
            <div style="margin-bottom: 8px;">
                <strong>Timestamp:</strong> ${timestamp}<br>
                <small>Unix: ${unixTimestamp}</small>
            </div>
            <div style="margin-bottom: 8px;">
                <strong>Open:</strong> ${formatPrice(candleData.open)}<br>
                <strong style="color: #26a69a;">High:</strong> ${formatPrice(candleData.high)}<br>
                <strong style="color: #ef5350;">Low:</strong> ${formatPrice(candleData.low)}<br>
                <strong>Close:</strong> ${formatPrice(candleData.close)}
            </div>
            <div style="margin-bottom: 8px;">
                <strong>Change:</strong> <span style="color: ${changeColor};">
                    ${changeSymbol}${formatPrice(Math.abs(priceChange))} (${changeSymbol}${priceChangePercent.toFixed(2)}%)
                </span>
            </div>
            <div style="margin-bottom: 8px;">
                <strong>Volume:</strong> ${formatVolume(volumeData?.value || 0)}
            </div>
            <div style="border-top: 1px solid #444; padding-top: 8px;">
                <strong>Current Price:</strong> <span style="color: #ffd700;">${formattedCurrentPrice}</span>
            </div>
        `;

        // Add indicator data if available
        if (window.indicatorsManager && typeof window.indicatorsManager.getIndicatorDataByTimestamp === 'function') {
            const indicatorData = window.indicatorsManager.getIndicatorDataByTimestamp(param.time);

            if (indicatorData) {
                let indicatorContent = '';

                // Add EMA values
                if (indicatorData.ema && Object.keys(indicatorData.ema).length > 0) {
                    indicatorContent += '<div style="margin-top: 8px; font-weight: bold; color: #26a69a;">EMA:</div>';
                    Object.keys(indicatorData.ema).forEach(emaKey => {
                        const period = emaKey.split('_')[1];
                        const value = indicatorData.ema[emaKey];
                        indicatorContent += `<div>EMA ${period}: ${formatPrice(value)}</div>`;
                    });
                }

                // Add Bollinger Bands values
                if (indicatorData.bollinger && Object.keys(indicatorData.bollinger).length > 0) {
                    indicatorContent += '<div style="margin-top: 8px; font-weight: bold; color: #9C27B0;">Bollinger Bands:</div>';
                    if (indicatorData.bollinger.bb_upper) {
                        indicatorContent += `<div>Upper: ${formatPrice(indicatorData.bollinger.bb_upper)}</div>`;
                    }
                    if (indicatorData.bollinger.bb_middle) {
                        indicatorContent += `<div>Middle: ${formatPrice(indicatorData.bollinger.bb_middle)}</div>`;
                    }
                    if (indicatorData.bollinger.bb_lower) {
                        indicatorContent += `<div>Lower: ${formatPrice(indicatorData.bollinger.bb_lower)}</div>`;
                    }
                }

                // Add MACD values
                if (indicatorData.macd && Object.keys(indicatorData.macd).length > 0) {
                    indicatorContent += '<div style="margin-top: 8px; font-weight: bold; color: #FF6D00;">MACD:</div>';
                    if (indicatorData.macd.macd_line) {
                        indicatorContent += `<div>MACD: ${indicatorData.macd.macd_line.toFixed(4)}</div>`;
                    }
                    if (indicatorData.macd.signal_line) {
                        indicatorContent += `<div>Signal: ${indicatorData.macd.signal_line.toFixed(4)}</div>`;
                    }
                    if (indicatorData.macd.histogram) {
                        indicatorContent += `<div>Histogram: ${indicatorData.macd.histogram.toFixed(4)}</div>`;
                    }
                }

                // Add RSI values
                if (indicatorData.rsi && Object.keys(indicatorData.rsi).length > 0) {
                    indicatorContent += '<div style="margin-top: 8px; font-weight: bold; color: #2196F3;">RSI:</div>';
                    Object.keys(indicatorData.rsi).forEach(rsiKey => {
                        const period = rsiKey.split('_')[1];
                        const value = indicatorData.rsi[rsiKey];
                        indicatorContent += `<div>RSI ${period}: ${value.toFixed(2)}</div>`;
                    });
                }

                if (indicatorContent) {
                    tooltipContent += `<div style="border-top: 1px solid #444; padding-top: 8px; margin-top: 8px;">${indicatorContent}</div>`;
                }
            }
        }

        this.ohlcvTooltip.innerHTML = tooltipContent;

        // Smart positioning logic
        this.positionTooltip(param.point);
    }

    positionTooltip(point) {
        if (!this.ohlcvTooltip || !point) return;

        // Show tooltip temporarily to get dimensions
        this.ohlcvTooltip.style.display = 'block';
        this.ohlcvTooltip.style.visibility = 'hidden';

        const containerRect = this.container.getBoundingClientRect();
        const tooltipRect = this.ohlcvTooltip.getBoundingClientRect();

        const margin = 15;
        const containerWidth = this.container.clientWidth;
        const containerHeight = this.container.clientHeight;

        // Default position (right and below cursor)
        let left = point.x + margin;
        let top = point.y + margin;

        // Check if tooltip would go off the right edge
        if (left + tooltipRect.width > containerWidth) {
            left = point.x - tooltipRect.width - margin; // Position to the left
        }

        // Check if tooltip would go off the bottom edge
        if (top + tooltipRect.height > containerHeight) {
            top = point.y - tooltipRect.height - margin; // Position above
        }

        // Ensure tooltip doesn't go off the left edge
        if (left < margin) {
            left = margin;
        }

        // Ensure tooltip doesn't go off the top edge
        if (top < margin) {
            top = margin;
        }

        // Final boundary checks
        if (left + tooltipRect.width > containerWidth - margin) {
            left = containerWidth - tooltipRect.width - margin;
        }
        if (top + tooltipRect.height > containerHeight - margin) {
            top = containerHeight - tooltipRect.height - margin;
        }

        this.ohlcvTooltip.style.left = `${Math.max(0, left)}px`;
        this.ohlcvTooltip.style.top = `${Math.max(0, top)}px`;
        this.ohlcvTooltip.style.visibility = 'visible';
    }

    updatePriceDisplay(param) {
        // Create price display if it doesn't exist
        if (!this.priceDisplay) {
            this.createOHLCVTooltip();
        }

        if (!param.point || param.point.y === undefined) {
            if (this.priceDisplay) {
                this.priceDisplay.style.display = 'none';
            }
            return;
        }

        // Get the price at the current cursor position
        const price = this.chart.coordinateToPrice(param.point.y);

        if (price === null || price === undefined) {
            this.priceDisplay.style.display = 'none';
            return;
        }

        // Format the price with appropriate precision
        let formattedPrice;
        if (price >= 1000) {
            formattedPrice = new Intl.NumberFormat('en-US', {
                style: 'currency',
                currency: 'USD',
                minimumFractionDigits: 2,
                maximumFractionDigits: 2
            }).format(price);
        } else if (price >= 1) {
            formattedPrice = new Intl.NumberFormat('en-US', {
                style: 'currency',
                currency: 'USD',
                minimumFractionDigits: 2,
                maximumFractionDigits: 4
            }).format(price);
        } else {
            formattedPrice = new Intl.NumberFormat('en-US', {
                style: 'currency',
                currency: 'USD',
                minimumFractionDigits: 4,
                maximumFractionDigits: 8
            }).format(price);
        }

        this.priceDisplay.textContent = formattedPrice;

        // Position the price display on the right edge, centered vertically with cursor
        const containerHeight = this.container.clientHeight;
        let top = param.point.y - 15; // Center on cursor

        // Keep within container bounds
        if (top < 5) top = 5;
        if (top > containerHeight - 35) top = containerHeight - 35;

        this.priceDisplay.style.top = `${top}px`;
        this.priceDisplay.style.display = 'block';
    }

    setupEventListeners() {
        if (this.config.autoResize) {
            window.addEventListener('resize', () => {
                this.handleResize();
            });
        }
    }
    
    handleResize() {
        if (this.chart && this.container) {
            this.chart.applyOptions({
                width: this.container.clientWidth,
                height: this.container.clientHeight || 600,
            });
        }
    }
    
    handleCrosshairMove(param) {
        if (!param.time || !param.seriesPrices) {
            this.updateCrosshairInfo(null);
            return;
        }

        try {
            const data = param.seriesPrices.get(this.candlestickSeries);
            const volumeData = param.seriesPrices.get(this.volumeSeries);

            if (data) {
                this.updateCrosshairInfo({
                    time: param.time,
                    open: data.open,
                    high: data.high,
                    low: data.low,
                    close: data.close,
                    volume: volumeData || 0
                });
            }
        } catch (error) {
            // Silently handle crosshair errors to prevent spam
            console.debug('Crosshair move error:', error);
        }
    }
    
    updateCrosshairInfo(data) {
        const infoElement = document.getElementById('crosshair-info');
        if (!infoElement) return;
        
        if (!data) {
            infoElement.textContent = '';
            return;
        }
        
        const time = new Date(data.time * 1000);
        const info = `${time.toLocaleString()} | O: ${data.open?.toFixed(2)} H: ${data.high?.toFixed(2)} L: ${data.low?.toFixed(2)} C: ${data.close?.toFixed(2)} V: ${data.volume?.toLocaleString()}`;
        infoElement.textContent = info;
    }
    
    async loadHistoricalData() {
        try {
            this.updateStatus('Loading initial data...');

            if (!this.dataFeed) {
                this.initializeDataFeed();
            }

            const initialData = await this.dataFeed.loadInitialData(500);

            if (initialData && initialData.length > 0) {
                this.candlestickSeries.setData(initialData);
                this.volumeSeries.setData(this.dataFeed.getVolumeData());
                this.currentData = initialData;

                // Fit content to show all data
                this.chart.timeScale().fitContent();

                // Reapply chart configuration after data loading
                this.applyChartConfiguration();

                // Update support and resistance levels
                setTimeout(() => this.updateSupportResistanceLevels(), 100);

                console.log(`Loaded ${initialData.length} candles for ${this.config.symbol}`);
                this.updateStatus(`Loaded ${initialData.length} candles for ${this.config.symbol} (scroll left for more history)`);
            } else {
                throw new Error('No data loaded');
            }
        } catch (error) {
            console.error('Error loading historical data:', error);
            this.updateStatus('Error loading data, using sample data');
            this.loadSampleData();
        }
    }

    loadSampleData() {
        try {
            // Generate realistic sample data for demonstration
            const sampleData = [];
            const volumeData = [];

            // Get interval in minutes
            const intervalMinutes = this.getIntervalMinutes(this.config.interval);
            const startTime = Math.floor(Date.now() / 1000) - (500 * intervalMinutes * 60); // 500 intervals ago

            let basePrice = this.config.symbol.includes('BTC') ? 45000 :
                           this.config.symbol.includes('ETH') ? 3000 : 100;

            for (let i = 0; i < 500; i++) {
                const time = startTime + (i * intervalMinutes * 60); // Proper interval spacing
                const volatility = basePrice * 0.002; // 0.2% volatility

                const open = i === 0 ? basePrice : sampleData[i - 1].close;
                const change = (Math.random() - 0.5) * volatility * 2;
                const close = open + change;
                const high = Math.max(open, close) + Math.random() * volatility;
                const low = Math.min(open, close) - Math.random() * volatility;
                const volume = Math.random() * 1000000 + 100000;

                sampleData.push({
                    time: time,
                    open: parseFloat(open.toFixed(2)),
                    high: parseFloat(high.toFixed(2)),
                    low: parseFloat(low.toFixed(2)),
                    close: parseFloat(close.toFixed(2)),
                });

                volumeData.push({
                    time: time,
                    value: parseFloat(volume.toFixed(0)),
                    color: close >= open ? '#26a69a80' : '#ef535080',
                });
            }

            this.candlestickSeries.setData(sampleData);
            this.volumeSeries.setData(volumeData);

            // Fit content to show all data
            this.chart.timeScale().fitContent();

            // Reapply chart configuration after sample data loading
            this.applyChartConfiguration();

            // Update support and resistance levels
            setTimeout(() => this.updateSupportResistanceLevels(), 100);

            this.updateStatus(`Sample data loaded for ${this.config.symbol} (${sampleData.length} candles)`);
        } catch (error) {
            console.error('Error loading sample data:', error);
            this.updateStatus('Error loading sample data', 'error');
        }
    }

    getIntervalMinutes(interval) {
        const intervalMap = {
            '1m': 1, '3m': 3, '5m': 5, '15m': 15, '30m': 30,
            '1h': 60, '2h': 120, '4h': 240, '6h': 360, '8h': 480, '12h': 720,
            '1d': 1440, '3d': 4320, '1w': 10080, '1M': 43200
        };
        return intervalMap[interval] || 1;
    }
    
    connectWebSocket() {
        if (this.websocket) {
            this.websocket.close();
        }
        
        const wsUrl = `ws://localhost:8000/api/v1/ws/kline?symbol=${this.config.symbol}&interval=${this.config.interval}`;
        
        try {
            this.websocket = new WebSocket(wsUrl);
            
            this.websocket.onopen = () => {
                console.log('WebSocket connected');
                this.isConnected = true;
                this.updateStatus(`Live data connected for ${this.config.symbol}`);
            };
            
            this.websocket.onmessage = (event) => {
                try {
                    const message = JSON.parse(event.data);
                    this.handleWebSocketMessage(message);
                } catch (error) {
                    // Only log parsing errors occasionally to prevent spam
                    if (Date.now() - this.lastErrorTime > 5000) {
                        console.error('Error parsing WebSocket message:', error);
                        this.lastErrorTime = Date.now();
                    }
                }
            };
            
            this.websocket.onclose = () => {
                console.log('WebSocket disconnected');
                this.isConnected = false;
                this.updateStatus('Live data disconnected');
                
                // Attempt to reconnect after 5 seconds
                setTimeout(() => {
                    if (!this.isConnected) {
                        this.connectWebSocket();
                    }
                }, 5000);
            };
            
            this.websocket.onerror = (error) => {
                console.error('WebSocket error:', error);
                this.updateStatus('WebSocket connection error', 'error');
            };
            
        } catch (error) {
            console.error('Error creating WebSocket connection:', error);
            this.updateStatus('Failed to connect to live data', 'error');
        }
    }
    
    handleWebSocketMessage(message) {
        try {
            if (message.type === 'kline' && message.data) {
                const klineData = message.data;

                // Ensure timestamp is properly formatted as Unix timestamp in seconds
                let timestamp;
                if (typeof klineData.timestamp === 'number') {
                    // If it's already a number, ensure it's in seconds
                    timestamp = klineData.timestamp > 1000000000000 ?
                        Math.floor(klineData.timestamp / 1000) :
                        Math.floor(klineData.timestamp);
                } else if (typeof klineData.timestamp === 'string') {
                    // If it's a string, parse it
                    timestamp = Math.floor(new Date(klineData.timestamp).getTime() / 1000);
                } else {
                    console.warn('Invalid timestamp format:', klineData.timestamp);
                    return;
                }

                // Validate timestamp is reasonable (not in the past or too far in future)
                const now = Math.floor(Date.now() / 1000);
                if (timestamp < now - 86400 || timestamp > now + 3600) { // Within 24h past to 1h future
                    console.warn('Timestamp out of reasonable range:', timestamp, 'current:', now);
                    return;
                }

                const candlePoint = {
                    time: timestamp,
                    open: parseFloat(klineData.open),
                    high: parseFloat(klineData.high),
                    low: parseFloat(klineData.low),
                    close: parseFloat(klineData.close),
                };

                const volumePoint = {
                    time: timestamp,
                    value: parseFloat(klineData.volume),
                    color: candlePoint.close >= candlePoint.open ? '#26a69a80' : '#ef535080',
                };

                // Validate OHLC data
                if (isNaN(candlePoint.open) || isNaN(candlePoint.high) ||
                    isNaN(candlePoint.low) || isNaN(candlePoint.close)) {
                    console.warn('Invalid OHLC data:', candlePoint);
                    return;
                }

                // Update the chart
                this.candlestickSeries.update(candlePoint);
                this.volumeSeries.update(volumePoint);

                // Update currentData array for support/resistance analysis
                this.updateCurrentDataWithNewCandle(candlePoint);

                // Update support and resistance levels with new data
                this.updateSupportResistanceLevels();

                this.lastUpdateTime = Date.now();

                // Update status
                this.updateStatus(`Live update: ${this.config.symbol} $${candlePoint.close.toFixed(2)}`);
            }
        } catch (error) {
            console.error('Error handling WebSocket message:', error);
            // Don't spam the console with repeated errors
            if (this.lastErrorTime !== Date.now()) {
                this.updateStatus('WebSocket data error', 'error');
                this.lastErrorTime = Date.now();
            }
        }
    }
    
    updateStatus(message, type = 'info') {
        const statusElement = document.getElementById('chart-status');
        if (statusElement) {
            statusElement.textContent = message;
            statusElement.className = `status ${type}`;
        }
        console.log(`Chart status: ${message}`);
    }
    
    changeSymbol(symbol) {
        this.config.symbol = symbol.toUpperCase();
        this.updateStatus(`Switching to ${symbol}...`);

        // Reset data feed for new symbol
        this.initializeDataFeed();
        this.loadHistoricalData();

        if (this.websocket) {
            this.websocket.close();
            this.connectWebSocket();
        }
    }

    changeInterval(interval) {
        this.config.interval = interval;
        this.updateStatus(`Changing to ${interval} timeframe...`);

        // Reset data feed for new interval
        this.initializeDataFeed();
        this.loadHistoricalData();

        if (this.websocket) {
            this.websocket.close();
            this.connectWebSocket();
        }
    }

    // Advanced chart features
    addPriceLines(levels) {
        /**
         * Add horizontal price lines for support/resistance levels
         * @param {Array} levels - Array of {price, color, style} objects
         */
        if (!levels || !Array.isArray(levels)) return;

        levels.forEach(level => {
            const priceLine = this.candlestickSeries.createPriceLine({
                price: level.price,
                color: level.color || '#758696',
                lineWidth: level.width || 1,
                lineStyle: level.style || 2, // Dashed
                axisLabelVisible: true,
                title: level.title || `${level.price}`,
            });
        });
    }

    addTradingMarkers(trades) {
        /**
         * Add buy/sell markers to the chart
         * @param {Array} trades - Array of trade objects
         */
        if (!trades || !Array.isArray(trades)) return;

        const markers = trades.map(trade => ({
            time: Math.floor(trade.timestamp),
            position: trade.side === 'buy' ? 'belowBar' : 'aboveBar',
            color: trade.side === 'buy' ? '#26a69a' : '#ef5350',
            shape: trade.side === 'buy' ? 'arrowUp' : 'arrowDown',
            text: `${trade.side.toUpperCase()} @ $${trade.price}`,
            size: 1,
        }));

        this.candlestickSeries.setMarkers(markers);
    }

    enableCrosshairMagnet(enabled = true) {
        /**
         * Enable crosshair magnet to snap to candle data points
         */
        this.chart.applyOptions({
            crosshair: {
                ...this.chart.options().crosshair,
                mode: enabled ?
                    LightweightCharts.CrosshairMode.Magnet :
                    LightweightCharts.CrosshairMode.Normal,
            }
        });
    }

    setChartTheme(theme = 'dark') {
        /**
         * Switch between light and dark themes
         */
        const themes = {
            dark: {
                backgroundColor: '#131722',
                textColor: '#d1d4dc',
                gridColor: '#363c4e',
                borderColor: '#485c7b',
            },
            light: {
                backgroundColor: '#FFFFFF',
                textColor: '#191919',
                gridColor: '#e1e3e6',
                borderColor: '#d0d3d7',
            }
        };

        const selectedTheme = themes[theme] || themes.dark;

        this.chart.applyOptions({
            layout: {
                backgroundColor: selectedTheme.backgroundColor,
                textColor: selectedTheme.textColor,
            },
            grid: {
                vertLines: { color: selectedTheme.gridColor },
                horzLines: { color: selectedTheme.gridColor },
            },
            rightPriceScale: {
                borderColor: selectedTheme.borderColor,
                textColor: selectedTheme.textColor,
            },
            timeScale: {
                borderColor: selectedTheme.borderColor,
                textColor: selectedTheme.textColor,
            },
        });

        this.config.theme = theme;
    }
    
    destroy() {
        if (this.websocket) {
            this.websocket.close();
        }

        // Clean up support/resistance lines
        this.removeSupportResistanceLines();

        if (this.chart) {
            this.chart.remove();
        }

        window.removeEventListener('resize', this.handleResize);
    }
}

// Global chart instance
let tradingViewChart = null;

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    // Wait for TradingView library to load
    const initChart = () => {
        if (typeof LightweightCharts !== 'undefined') {
            tradingViewChart = new TradingViewChart('tradingview-chart');
            window.tradingViewChart = tradingViewChart;
            
            // Load initial data
            tradingViewChart.loadHistoricalData();
            
            console.log('TradingView chart initialized');
        } else {
            console.log('Waiting for TradingView library...');
            setTimeout(initChart, 100);
        }
    };
    
    initChart();
});
