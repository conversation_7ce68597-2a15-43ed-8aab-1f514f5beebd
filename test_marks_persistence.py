#!/usr/bin/env python3
"""
Test script to verify that manual_marks table data persists across application restarts.
This script should be run to verify the fix for automatic table cleanup.
"""

import sys
import os
import time
from datetime import datetime

# Add the backend directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from app.core.database import get_db_cursor, init_db
import asyncio

def test_marks_persistence():
    """Test that manual marks persist across database initialization"""
    
    print("🧪 Testing Manual Marks Persistence")
    print("=" * 50)
    
    try:
        # Step 1: Check if table exists and count existing records
        print("📊 Step 1: Checking existing data...")
        with get_db_cursor(dict_cursor=True) as cursor:
            cursor.execute("SELECT COUNT(*) as count FROM manual_marks")
            initial_count = cursor.fetchone()['count']
            print(f"   Initial record count: {initial_count}")
            
            # Get existing records for comparison
            cursor.execute("SELECT id, timestamp, price, mark_type FROM manual_marks ORDER BY id")
            existing_records = cursor.fetchall()
            print(f"   Existing records: {len(existing_records)}")
            for record in existing_records[:3]:  # Show first 3
                print(f"     - ID {record['id']}: {record['mark_type']} at {record['price']} on {record['timestamp']}")
            if len(existing_records) > 3:
                print(f"     ... and {len(existing_records) - 3} more")
        
        # Step 2: Add a test record to verify it persists
        print("\n📝 Step 2: Adding test record...")
        test_timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        test_price = 50000.12345
        
        with get_db_cursor(dict_cursor=True) as cursor:
            insert_sql = """
                INSERT INTO manual_marks (
                    symbol, timeframe, mark_type, entry_side, timestamp, price,
                    indicator_snapshot, ohlcv_snapshot, linked_trade_id
                ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
            """
            
            cursor.execute(insert_sql, (
                'BTCUSDT', '15m', 'ENTRY', 'BUY', test_timestamp, test_price,
                '{"test": "persistence_check"}', '{"test": "ohlcv_data"}', None
            ))
            
            test_record_id = cursor.lastrowid
            print(f"   ✅ Test record added with ID: {test_record_id}")
        
        # Step 3: Simulate application restart by calling init_db
        print("\n🔄 Step 3: Simulating application restart (calling init_db)...")
        
        async def run_init():
            await init_db()
        
        asyncio.run(run_init())
        print("   ✅ Database initialization completed")
        
        # Step 4: Verify data still exists
        print("\n🔍 Step 4: Verifying data persistence...")
        with get_db_cursor(dict_cursor=True) as cursor:
            cursor.execute("SELECT COUNT(*) as count FROM manual_marks")
            final_count = cursor.fetchone()['count']
            print(f"   Final record count: {final_count}")
            
            # Check if our test record still exists
            cursor.execute("SELECT * FROM manual_marks WHERE id = %s", (test_record_id,))
            test_record = cursor.fetchone()
            
            if test_record:
                print(f"   ✅ Test record still exists: ID {test_record['id']}")
                print(f"      Price: {test_record['price']}")
                print(f"      Timestamp: {test_record['timestamp']}")
                print(f"      Indicator snapshot: {test_record['indicator_snapshot']}")
            else:
                print("   ❌ Test record was deleted!")
                return False
            
            # Verify all original records still exist
            cursor.execute("SELECT id, timestamp, price, mark_type FROM manual_marks ORDER BY id")
            final_records = cursor.fetchall()
            
            if len(final_records) >= len(existing_records):
                print(f"   ✅ All original records preserved ({len(existing_records)} records)")
                if len(final_records) > len(existing_records):
                    print(f"   📈 Plus {len(final_records) - len(existing_records)} new records")
            else:
                print(f"   ❌ Some records were lost! Expected: {len(existing_records)}, Found: {len(final_records)}")
                return False
        
        # Step 5: Clean up test record
        print("\n🧹 Step 5: Cleaning up test record...")
        with get_db_cursor(dict_cursor=True) as cursor:
            cursor.execute("DELETE FROM manual_marks WHERE id = %s", (test_record_id,))
            print(f"   ✅ Test record {test_record_id} removed")
        
        print("\n🎉 SUCCESS: Manual marks persistence test PASSED!")
        print("   ✅ Data is preserved across application restarts")
        print("   ✅ Database initialization no longer drops existing data")
        return True
        
    except Exception as e:
        print(f"\n❌ ERROR: Test failed with exception: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_clear_button_functionality():
    """Test that the clear button API endpoint works correctly"""
    
    print("\n🧪 Testing Clear Button Functionality")
    print("=" * 50)
    
    try:
        # This would normally be tested through the API, but we can test the database operation
        print("📝 Adding test records for clear functionality test...")
        
        test_records = []
        with get_db_cursor(dict_cursor=True) as cursor:
            for i in range(3):
                insert_sql = """
                    INSERT INTO manual_marks (
                        symbol, timeframe, mark_type, entry_side, timestamp, price,
                        indicator_snapshot, ohlcv_snapshot, linked_trade_id
                    ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
                """
                
                cursor.execute(insert_sql, (
                    'BTCUSDT', '15m', 'ENTRY', 'BUY', 
                    datetime.now().strftime('%Y-%m-%d %H:%M:%S'), 
                    50000 + i, '{}', '{}', None
                ))
                
                test_records.append(cursor.lastrowid)
            
            print(f"   ✅ Added {len(test_records)} test records: {test_records}")
            
            # Verify they exist
            cursor.execute("SELECT COUNT(*) as count FROM manual_marks WHERE id IN %s", (tuple(test_records),))
            count = cursor.fetchone()['count']
            print(f"   📊 Confirmed {count} test records exist")
            
            # Simulate clear operation (what the API does)
            print("\n🗑️ Simulating clear all marks operation...")
            cursor.execute("SELECT COUNT(*) as count FROM manual_marks")
            before_count = cursor.fetchone()['count']
            
            cursor.execute("DELETE FROM manual_marks")
            
            cursor.execute("SELECT COUNT(*) as count FROM manual_marks")
            after_count = cursor.fetchone()['count']
            
            print(f"   📊 Records before clear: {before_count}")
            print(f"   📊 Records after clear: {after_count}")
            
            if after_count == 0:
                print("   ✅ Clear operation successful - all records removed")
                return True
            else:
                print(f"   ❌ Clear operation failed - {after_count} records remain")
                return False
                
    except Exception as e:
        print(f"\n❌ ERROR: Clear button test failed: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Manual Marks Persistence Test Suite")
    print("=" * 60)
    
    # Test 1: Persistence across restarts
    persistence_result = test_marks_persistence()
    
    # Test 2: Clear button functionality
    clear_result = test_clear_button_functionality()
    
    print("\n" + "=" * 60)
    print("📋 TEST RESULTS SUMMARY")
    print("=" * 60)
    print(f"✅ Persistence Test: {'PASSED' if persistence_result else 'FAILED'}")
    print(f"✅ Clear Button Test: {'PASSED' if clear_result else 'FAILED'}")
    
    if persistence_result and clear_result:
        print("\n🎉 ALL TESTS PASSED!")
        print("   ✅ Manual marks will persist across application restarts")
        print("   ✅ Clear button functionality works correctly")
        print("   ✅ Data is only cleared when user explicitly confirms")
    else:
        print("\n❌ SOME TESTS FAILED!")
        print("   Please check the error messages above")
        sys.exit(1)
