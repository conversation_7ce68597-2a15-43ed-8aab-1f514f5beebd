<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Chart Loading</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #131722;
            color: white;
            font-family: 'Segoe UI', sans-serif;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            background: rgba(38, 166, 154, 0.1);
            border-left: 3px solid #26a69a;
            border-radius: 4px;
        }
        
        .chart-container {
            width: 100%;
            height: 400px;
            border: 1px solid #363c4e;
            border-radius: 8px;
            margin: 20px 0;
        }
        
        .controls {
            margin: 20px 0;
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        
        button {
            padding: 10px 20px;
            background: #2962ff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        
        button:hover {
            background: #1e53e5;
        }
        
        button:disabled {
            background: #666;
            cursor: not-allowed;
        }
        
        .status {
            margin-top: 20px;
            padding: 15px;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
        
        .log-entry {
            margin: 2px 0;
            padding: 2px 0;
        }
        
        .log-success { color: #26a69a; }
        .log-error { color: #ef5350; }
        .log-warning { color: #ff9800; }
        .log-info { color: #2196f3; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Chart Loading Test</h1>
        
        <div class="test-section">
            <h2>Chart Loading Functionality Test</h2>
            <p>This test simulates the "LOAD FROM DB" functionality to identify why the chart doesn't update.</p>
            
            <div class="chart-container" id="test-chart"></div>
            
            <div class="controls">
                <button id="create-chart">1. Create Chart</button>
                <button id="load-sample-data" disabled>2. Load Sample Data</button>
                <button id="simulate-db-load" disabled>3. Simulate DB Load</button>
                <button id="clear-chart" disabled>4. Clear Chart</button>
                <button id="clear-log">Clear Log</button>
            </div>
            
            <div class="status" id="log-output">
                <div class="log-info">🔄 Ready to test chart loading...</div>
            </div>
        </div>
    </div>

    <!-- TradingView Lightweight Charts -->
    <script src="https://unpkg.com/lightweight-charts/dist/lightweight-charts.standalone.production.js"></script>
    
    <script>
        let testChart = null;
        let candlestickSeries = null;
        let volumeSeries = null;
        
        function log(message, type = 'info') {
            const logOutput = document.getElementById('log-output');
            const entry = document.createElement('div');
            entry.className = `log-entry log-${type}`;
            entry.textContent = `${new Date().toLocaleTimeString()} - ${message}`;
            logOutput.appendChild(entry);
            logOutput.scrollTop = logOutput.scrollHeight;
            console.log(`[${type.toUpperCase()}] ${message}`);
        }
        
        function updateButtons() {
            const hasChart = !!testChart;
            document.getElementById('load-sample-data').disabled = !hasChart;
            document.getElementById('simulate-db-load').disabled = !hasChart;
            document.getElementById('clear-chart').disabled = !hasChart;
        }
        
        // Generate sample OHLCV data similar to what comes from the database
        function generateSampleOHLCVData(count = 100) {
            const data = [];
            let price = 50000;
            const startTime = new Date();
            startTime.setHours(startTime.getHours() - count);
            
            for (let i = 0; i < count; i++) {
                const timestamp = new Date(startTime.getTime() + (i * 15 * 60 * 1000)); // 15-minute intervals
                const change = (Math.random() - 0.5) * 1000;
                const open = price;
                const close = price + change;
                const high = Math.max(open, close) + Math.random() * 500;
                const low = Math.min(open, close) - Math.random() * 500;
                const volume = Math.random() * 1000000 + 500000;
                
                data.push({
                    timestamp: timestamp.toISOString(),
                    open: open.toFixed(2),
                    high: high.toFixed(2),
                    low: low.toFixed(2),
                    close: close.toFixed(2),
                    volume: volume.toFixed(0)
                });
                
                price = close;
            }
            
            return data;
        }
        
        // Simulate the loadDataToChart function from strategy-manager.js
        function loadDataToChart(ohlcvData) {
            log(`🔄 loadDataToChart called with ${ohlcvData.length} items`);
            
            if (!testChart) {
                log('❌ Chart not available', 'error');
                return false;
            }
            
            if (!candlestickSeries || !volumeSeries) {
                log('❌ Chart series not initialized', 'error');
                return false;
            }
            
            try {
                log('📊 Processing OHLCV data...');
                log(`Sample data item: ${JSON.stringify(ohlcvData[0])}`);
                
                // Process data for chart (same logic as strategy-manager.js)
                const candleData = ohlcvData.map(item => ({
                    time: Math.floor(new Date(item.timestamp).getTime() / 1000),
                    open: parseFloat(item.open),
                    high: parseFloat(item.high),
                    low: parseFloat(item.low),
                    close: parseFloat(item.close),
                })).sort((a, b) => a.time - b.time);
                
                log(`📈 Processed candle data: ${candleData.length} items`);
                log(`Sample candle: ${JSON.stringify(candleData[0])}`);
                
                const volumeData = ohlcvData.map(item => ({
                    time: Math.floor(new Date(item.timestamp).getTime() / 1000),
                    value: parseFloat(item.volume),
                    color: parseFloat(item.close) >= parseFloat(item.open) ? '#26a69a80' : '#ef535080',
                })).sort((a, b) => a.time - b.time);
                
                log(`📊 Processed volume data: ${volumeData.length} items`);
                
                // Update chart
                log('🔄 Updating chart series...');
                candlestickSeries.setData(candleData);
                log('✅ Candlestick data set');
                
                volumeSeries.setData(volumeData);
                log('✅ Volume data set');
                
                testChart.timeScale().fitContent();
                log('✅ Chart fitted to content');
                
                log(`✅ Successfully loaded ${candleData.length} candles to chart`, 'success');
                return true;
                
            } catch (error) {
                log(`❌ Error loading data to chart: ${error.message}`, 'error');
                log(`Error details: ${error.stack}`, 'error');
                return false;
            }
        }
        
        // Event handlers
        document.getElementById('create-chart').addEventListener('click', () => {
            try {
                log('🔄 Creating chart...');
                
                const container = document.getElementById('test-chart');
                testChart = LightweightCharts.createChart(container, {
                    width: container.clientWidth,
                    height: 400,
                    layout: {
                        backgroundColor: '#131722',
                        textColor: '#d1d4dc',
                    },
                    grid: {
                        vertLines: { color: '#363c4e' },
                        horzLines: { color: '#363c4e' },
                    },
                    rightPriceScale: {
                        borderColor: '#485c7b',
                    },
                    timeScale: {
                        borderColor: '#485c7b',
                        timeVisible: true,
                    },
                });
                
                // Create series
                candlestickSeries = testChart.addCandlestickSeries({
                    upColor: '#26a69a',
                    downColor: '#ef5350',
                    borderVisible: false,
                    wickUpColor: '#26a69a',
                    wickDownColor: '#ef5350',
                });
                
                volumeSeries = testChart.addHistogramSeries({
                    color: '#26a69a',
                    priceFormat: { type: 'volume' },
                    priceScaleId: '',
                    scaleMargins: { top: 0.8, bottom: 0 },
                });
                
                log('✅ Chart created successfully', 'success');
                updateButtons();
                
            } catch (error) {
                log(`❌ Error creating chart: ${error.message}`, 'error');
            }
        });
        
        document.getElementById('load-sample-data').addEventListener('click', () => {
            const sampleData = generateSampleOHLCVData(50);
            log(`📊 Generated ${sampleData.length} sample data points`);
            
            const success = loadDataToChart(sampleData);
            if (success) {
                log('✅ Sample data loaded successfully', 'success');
            } else {
                log('❌ Failed to load sample data', 'error');
            }
        });
        
        document.getElementById('simulate-db-load').addEventListener('click', () => {
            log('🔄 Simulating database load with 56,203 candles...');
            
            // Generate a large dataset similar to what the API returns
            const largeDataset = generateSampleOHLCVData(1000); // Reduced for performance
            log(`📊 Generated ${largeDataset.length} data points (simulating 56,203)`);
            
            const success = loadDataToChart(largeDataset);
            if (success) {
                log('✅ Database simulation loaded successfully', 'success');
            } else {
                log('❌ Failed to load database simulation', 'error');
            }
        });
        
        document.getElementById('clear-chart').addEventListener('click', () => {
            if (candlestickSeries && volumeSeries) {
                candlestickSeries.setData([]);
                volumeSeries.setData([]);
                log('✅ Chart cleared', 'success');
            }
        });
        
        document.getElementById('clear-log').addEventListener('click', () => {
            document.getElementById('log-output').innerHTML = '<div class="log-info">🔄 Log cleared...</div>';
        });
        
        // Handle resize
        window.addEventListener('resize', () => {
            if (testChart) {
                testChart.applyOptions({ width: document.getElementById('test-chart').clientWidth });
            }
        });
        
        log('🚀 Chart loading test ready');
    </script>
</body>
</html>
