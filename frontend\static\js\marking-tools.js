/**
 * Marking Tools for TradingView Chart
 * Handles entry/exit marking with database integration
 */

class MarkingTools {
    constructor(chart) {
        this.chart = chart;
        this.marks = new Map(); // Store marks by ID
        this.currentClickData = null;
        this.currentModalType = null; // Track current modal type ('entry' or 'exit')
        this.currentTradeData = null; // Store extracted trade data for form submission
        this.isMarkingMode = false;
        
        this.initializeEventListeners();
        this.loadExistingMarks();
    }

    initializeEventListeners() {
        // Chart click handlers using TradingView's built-in events
        if (this.chart && this.chart.chart) {
            console.log('Setting up chart click handlers for marking tools');

            // Use TradingView's click event
            this.chart.chart.subscribeClick((param) => {
                console.log('Chart clicked via TradingView API, marking mode:', this.isMarkingMode);
                console.log('Click param:', param);

                if (this.isMarkingMode && param.point) {
                    console.log('Processing chart click for entry marking');
                    this.handleTradingViewClick(param, 'entry');
                }
            });

            // For right-click, we still need to use DOM events on the container
            if (this.chart.container) {
                this.chart.container.addEventListener('contextmenu', (e) => {
                    e.preventDefault();
                    console.log('Chart container right-clicked, marking mode:', this.isMarkingMode);
                    if (this.isMarkingMode) {
                        console.log('Chart right-clicked for exit marking');
                        this.handleChartClick(e, 'exit');
                    }
                });
            }

            console.log('Chart click handlers set up successfully');
        } else {
            console.warn('Chart not ready for marking tools event listeners');
            console.log('Chart:', this.chart);
            console.log('Chart.chart:', this.chart?.chart);
            console.log('Chart.container:', this.chart?.container);
        }

        // Modal event listeners
        this.initializeModalListeners();

        // Sidebar event listeners
        this.initializeSidebarListeners();
    }

    initializeModalListeners() {
        // Unified trade modal
        const tradeModal = document.getElementById('trade-modal');
        const tradeClose = document.getElementById('trade-modal-close');
        const tradeCancel = document.getElementById('trade-cancel');
        const tradeConfirm = document.getElementById('trade-confirm');

        if (tradeClose) {
            tradeClose.addEventListener('click', () => this.closeModal('trade-modal'));
        }

        if (tradeCancel) {
            tradeCancel.addEventListener('click', () => this.closeModal('trade-modal'));
        }

        if (tradeConfirm) {
            tradeConfirm.addEventListener('click', () => this.confirmTrade());
        }

        // Trade type dropdown change listener
        const tradeTypeSelect = document.getElementById('trade-type');
        if (tradeTypeSelect) {
            tradeTypeSelect.addEventListener('change', (e) => {
                const selectedType = e.target.value;
                console.log(`🔄 Trade type changed to: ${selectedType}`);
                this.configureFormForTradeType(selectedType);
            });
        }

        // Close modals when clicking outside
        window.addEventListener('click', (e) => {
            if (e.target.classList.contains('modal')) {
                this.closeModal(e.target.id);
            }
        });
    }

    initializeSidebarListeners() {
        // Export marks
        document.getElementById('export-marks').addEventListener('click', () => {
            this.exportMarks();
        });

        // Clear all marks
        document.getElementById('clear-all-marks').addEventListener('click', () => {
            this.clearAllMarks();
        });
    }

    handleTradingViewClick(param, type) {
        console.log(`handleTradingViewClick called with type: ${type}`);
        console.log('TradingView param:', param);

        if (!param.point || !param.time) {
            console.warn('Invalid click parameter from TradingView');
            return;
        }

        // Extract candlestick data from TradingView seriesData Map
        let candlestickData = null;
        let price = null;

        console.log('Param seriesData:', param.seriesData);

        if (param.seriesData && param.seriesData instanceof Map) {
            // Iterate through the Map to find candlestick data
            for (const [key, value] of param.seriesData) {
                console.log('SeriesData entry - Key:', key, 'Value:', value);

                // Check if this looks like candlestick data
                if (value && typeof value === 'object' &&
                    value.open !== undefined && value.high !== undefined &&
                    value.low !== undefined && value.close !== undefined) {

                    candlestickData = {
                        time: value.time || param.time,
                        open: value.open,
                        high: value.high,
                        low: value.low,
                        close: value.close,
                        volume: value.volume || 0
                    };

                    price = value.close;
                    console.log('✅ Found candlestick data from seriesData:', candlestickData);
                    break;
                }
            }
        }

        // Fallback: try other methods if seriesData didn't work
        if (!candlestickData) {
            console.log('No data in seriesData, trying other methods...');
            candlestickData = this.getCandlestickAtTime(param.time);

            if (candlestickData) {
                price = candlestickData.close;
            } else {
                // Final fallback: use coordinate-based price
                console.warn('Could not find candlestick data, using coordinate-based price');
                try {
                    const priceScale = this.chart.chart.priceScale('right');
                    price = priceScale.coordinateToPrice(param.point.y);
                } catch (error) {
                    console.warn('Could not get price from coordinates:', error);
                    price = 50000; // Default fallback
                }
            }
        }

        console.log(`Found candlestick:`, candlestickData);
        console.log(`Using candlestick close price: ${price} for time: ${param.time}`);

        this.currentClickData = {
            time: param.time,
            price: price,
            x: param.point.x,
            y: param.point.y,
            candlestickData: candlestickData // Use consistent property name
        };

        console.log('Current click data from TradingView:', this.currentClickData);

        // Always show the same modal, but pre-select the trade type
        console.log(`Showing trade modal with pre-selected type: ${type}`);
        this.showTradeModal(type);
    }

    handleChartClick(event, type) {
        console.log(`handleChartClick called with type: ${type}`);
        console.log('Event:', event);
        console.log('Chart container:', this.chart.container);

        const rect = this.chart.container.getBoundingClientRect();
        const x = event.clientX - rect.left;
        const y = event.clientY - rect.top;

        console.log(`Click coordinates: x=${x}, y=${y}`);

        // Get time from coordinates
        let time;
        try {
            time = this.chart.chart.timeScale().coordinateToTime(x);
            console.log(`Converted time: ${time}`);
        } catch (error) {
            console.warn('Could not get time from coordinates:', error);
            time = Math.floor(Date.now() / 1000);
        }

        // Get the exact candlestick data for the clicked time
        const candlestickData = this.getCandlestickAtTime(time);

        let price;
        if (candlestickData) {
            // Use the candlestick's close price for accuracy
            price = candlestickData.close;
        } else {
            // Fallback: use coordinate-based price
            console.warn('Could not find candlestick data, using coordinate-based price');
            try {
                const priceScale = this.chart.chart.priceScale('right');
                price = priceScale.coordinateToPrice(y);
            } catch (error) {
                console.warn('Could not get price from coordinates:', error);
                price = 50000; // Default fallback
            }
        }

        console.log(`Using candlestick data:`, candlestickData);

        this.currentClickData = {
            time: candlestickData ? candlestickData.time : time, // Use candlestick time if available, otherwise use coordinate time
            price: price,
            x: x,
            y: y,
            candlestickData: candlestickData // Use consistent property name
        };

        console.log('Current click data:', this.currentClickData);

        // Always show the same modal, but pre-select the trade type
        console.log(`Showing trade modal with pre-selected type: ${type}`);
        this.showTradeModal(type);
    }

    async showTradeModal(preselectedType = 'entry') {
        console.log(`🚀 showTradeModal called with preselected type: ${preselectedType}`);
        console.log('🔍 Current click data:', this.currentClickData);

        const modal = document.getElementById('trade-modal');

        if (!modal) {
            console.error('❌ Trade modal not found!');
            return;
        }

        // Validate click data
        if (!this.currentClickData) {
            console.error('❌ No click data available for trade modal');
            alert('Error: No click data available. Please click on the chart first.');
            return;
        }

        if (!this.currentClickData.time) {
            console.error('❌ No timestamp in click data:', this.currentClickData);
            alert('Error: Invalid click data - missing timestamp.');
            return;
        }

        if (!this.currentClickData.price) {
            console.error('❌ No price in click data:', this.currentClickData);
            alert('Error: Invalid click data - missing price.');
            return;
        }

        // Get current market data
        const symbol = this.getCurrentSymbol();
        const timeframe = this.getCurrentTimeframe();

        // Extract real price from candlestick data if available, otherwise use click price
        let actualPrice = this.currentClickData.price;
        let actualTime = this.currentClickData.time;

        // If we have candlestick data, use the close price for accuracy
        if (this.currentClickData.candlestickData) {
            console.log(`✅ Trade modal - Using candlestick data for accurate price:`, this.currentClickData.candlestickData);
            actualPrice = this.currentClickData.candlestickData.close;
            actualTime = this.currentClickData.candlestickData.time;
        } else {
            console.log(`⚠️  Trade modal - No candlestick data available, using click price:`, actualPrice);
        }

        const date = new Date(actualTime * 1000);
        const formattedTime = date.toLocaleString();
        const formattedPrice = `$${actualPrice.toFixed(2)}`;

        console.log(`📊 Trade modal market data:`, {
            symbol,
            timeframe,
            time: actualTime,
            price: actualPrice,
            hasCandlestickData: !!this.currentClickData.candlestickData
        });

        // Store the extracted trade data for form submission
        this.currentTradeData = {
            symbol: symbol,
            timeframe: timeframe,
            timestamp: actualTime,
            price: actualPrice,
            formattedTime: formattedTime,
            formattedPrice: formattedPrice,
            candlestickData: this.currentClickData.candlestickData
        };

        console.log(`💾 Stored trade data:`, this.currentTradeData);

        // Populate market data with real values
        this.setElementText('trade-symbol', symbol);
        this.setElementText('trade-timeframe', timeframe);
        this.setElementText('trade-time', formattedTime);
        this.setElementTextWithHover('trade-price', formattedPrice, this.createPriceTooltip(actualPrice));

        // Set the preselected trade type in dropdown
        const tradeTypeSelect = document.getElementById('trade-type');
        if (tradeTypeSelect) {
            tradeTypeSelect.value = preselectedType;
            console.log(`📋 Preselected trade type: ${preselectedType}`);
        }

        // Get and display OHLCV data using actual timestamp
        console.log(`🔍 Trade modal - extracting OHLCV data for timestamp:`, actualTime);
        const ohlcvData = await this.getOHLCVData(actualTime);
        console.log(`📈 Trade modal - OHLCV extraction result:`, ohlcvData);

        if (ohlcvData) {
            this.populateOHLCVData('trade-ohlcv', ohlcvData);
            // Show OHLCV section
            const ohlcvContainer = document.getElementById('trade-ohlcv');
            const ohlcvSection = ohlcvContainer ? ohlcvContainer.closest('.ohlcv-section') : null;
            if (ohlcvSection) {
                ohlcvSection.style.display = 'block';
            }
        } else {
            // Hide OHLCV section if no data available
            const ohlcvContainer = document.getElementById('trade-ohlcv');
            const ohlcvSection = ohlcvContainer ? ohlcvContainer.closest('.ohlcv-section') : null;
            if (ohlcvSection) {
                ohlcvSection.style.display = 'none';
            }
        }

        // Get and display indicator data using actual timestamp
        console.log(`🔍 Trade modal - extracting indicator data for timestamp:`, actualTime);
        const indicatorData = await this.getIndicatorData(actualTime);
        console.log(`📊 Trade modal - indicator data result:`, indicatorData);

        if (indicatorData) {
            this.populateIndicatorData('trade-indicators', indicatorData);
        } else {
            this.populateIndicatorData('trade-indicators', null);
        }

        // Configure form sections based on preselected type
        this.configureFormForTradeType(preselectedType);

        // Show the modal
        modal.style.display = 'block';
        console.log(`✅ Trade modal populated and displayed with preselected type: ${preselectedType}`);
    }

    configureFormForTradeType(tradeType) {
        console.log(`🔧 Configuring form for trade type: ${tradeType}`);

        const entryForm = document.getElementById('trade-entry-form');
        const exitForm = document.getElementById('trade-exit-form');
        const pnlSection = document.getElementById('trade-pnl-section');
        const confirmButton = document.getElementById('trade-confirm');

        if (tradeType === 'exit') {
            // Show exit form and P&L section
            if (exitForm) exitForm.style.display = 'block';
            if (pnlSection) pnlSection.style.display = 'block';

            // Update button text
            if (confirmButton) confirmButton.textContent = 'Add Exit';

            // Populate open entries for exit
            const entrySelect = document.getElementById('trade-entry-select');
            if (entrySelect) {
                this.populateOpenEntries(entrySelect);

                // Set up P&L calculation when entry is selected
                entrySelect.addEventListener('change', () => {
                    this.updatePnLCalculation();
                });
            }
        } else {
            // Hide exit form and P&L section for entry
            if (exitForm) exitForm.style.display = 'none';
            if (pnlSection) pnlSection.style.display = 'none';

            // Update button text
            if (confirmButton) confirmButton.textContent = 'Add Entry';

            // Reset entry form
            const sideSelect = document.getElementById('trade-side');
            const quantityInput = document.getElementById('trade-quantity');
            const notesInput = document.getElementById('trade-notes');

            if (sideSelect) sideSelect.value = 'buy';
            if (quantityInput) quantityInput.value = '';
            if (notesInput) notesInput.value = '';
        }

        console.log(`✅ Form configured for ${tradeType} type`);
    }

    populateOpenEntries(selectElement) {
        // Clear existing options except the first one
        selectElement.innerHTML = '<option value="">Select an open entry...</option>';

        // Add entry marks (show all entry marks that don't have corresponding exits)
        console.log('🔍 Populating open entries, total marks:', this.marks.size);

        this.marks.forEach((mark, id) => {
            console.log('📋 Checking mark:', id, mark);

            // Handle both old and new schema formats
            // For new schema: mark_type === 'ENTRY' and no linked exit
            // For old schema: status === 'open' or no exit_timestamp
            const isEntryMark = mark.mark_type === 'ENTRY' ||
                               mark.status === 'open' ||
                               (!mark.exit_timestamp && !mark.linked_trade_id);

            if (isEntryMark) {
                let side = mark.side || (mark.entry_side ? mark.entry_side.toLowerCase() : null) || 'buy';

                // Ensure side is a valid string before calling toUpperCase
                if (!side || typeof side !== 'string') {
                    side = 'buy'; // Default fallback
                }

                const entryPrice = mark.entry_price || mark.price || 0;
                const quantity = mark.quantity || 1; // Default quantity for new schema
                const timestamp = mark.entry_timestamp || mark.timestamp;
                const dateStr = timestamp ? new Date(timestamp * 1000).toLocaleString() : 'Unknown';

                const option = document.createElement('option');
                option.value = id;
                option.textContent = `${side.toUpperCase()} @ $${entryPrice.toFixed(2)} (${quantity}) - ${dateStr}`;
                selectElement.appendChild(option);

                console.log('✅ Added entry option:', option.textContent);
            } else {
                console.log('⏭️  Skipping mark (not an open entry):', mark);
            }
        });

        console.log('📊 Total entry options added:', selectElement.children.length - 1);
    }

    async confirmTrade() {
        // Get the selected trade type from dropdown
        const tradeTypeSelect = document.getElementById('trade-type');
        const selectedType = tradeTypeSelect ? tradeTypeSelect.value : 'entry';

        console.log(`🔄 confirmTrade called for selected type: ${selectedType}`);

        if (selectedType === 'entry') {
            await this.confirmEntry();
        } else if (selectedType === 'exit') {
            await this.confirmExit();
        } else {
            console.error('❌ Unknown trade type:', selectedType);
            alert('Error: Unknown trade type');
        }
    }

    async confirmEntry() {
        const side = document.getElementById('trade-side').value;
        const quantity = parseFloat(document.getElementById('trade-quantity').value);
        const notes = document.getElementById('trade-notes').value;

        // Validate side field
        if (!side || (side !== 'buy' && side !== 'sell')) {
            alert('Please select a valid entry side (buy or sell)');
            return;
        }

        if (!quantity || quantity <= 0) {
            alert('Please enter a valid quantity');
            return;
        }

        // Validate stored trade data
        if (!this.currentTradeData) {
            console.error('❌ No stored trade data available');
            alert('Error: Trade data not available. Please close and reopen the modal.');
            return;
        }

        // Get comprehensive market data using stored timestamp
        const symbol = this.currentTradeData.symbol;
        const timeframe = this.currentTradeData.timeframe;
        const ohlcvData = await this.getOHLCVData(this.currentTradeData.timestamp);
        const indicatorData = await this.getIndicatorData(this.currentTradeData.timestamp);

        const entryData = {
            timestamp: this.currentTradeData.timestamp,
            price: this.currentTradeData.price,
            side: side.toLowerCase(), // Ensure consistent lowercase format
            quantity: quantity,
            notes: notes,
            ohlcv_data: ohlcvData,
            indicator_data: indicatorData
        };

        // Store comprehensive data for local use
        const comprehensiveData = {
            symbol: symbol,
            timeframe: timeframe,
            entry: {
                timestamp: new Date(this.currentTradeData.timestamp * 1000).toISOString(),
                entry_side: side ? side.charAt(0).toUpperCase() + side.slice(1) : 'Buy', // Safe toUpperCase with fallback
                price: this.currentTradeData.price,
                ohlcv: ohlcvData,
                indicators: indicatorData
            }
        };

        console.log('Comprehensive entry data:', comprehensiveData);
        console.log('Entry data being sent to server:', entryData);
        console.log('Timestamp details:', {
            storedTime: this.currentTradeData.timestamp,
            timestampType: typeof this.currentTradeData.timestamp,
            dateFromTimestamp: new Date(this.currentTradeData.timestamp * 1000),
            candlestickTime: this.currentTradeData.candlestickData?.time
        });

        try {
            const response = await fetch('/api/v1/marks/entry', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(entryData)
            });

            if (!response.ok) {
                const errorText = await response.text();
                console.error('Server error response:', errorText);
                throw new Error(`Server error: ${response.status} - ${errorText}`);
            }

            const result = await response.json();

            if (result.success) {
                // Store comprehensive data with the mark
                result.data.comprehensiveData = comprehensiveData;

                // Ensure backward compatibility properties for entry selection
                result.data.status = 'open';
                result.data.side = entryData.side;
                result.data.quantity = entryData.quantity;
                result.data.notes = entryData.notes;

                // Add mark to chart
                this.addMarkToChart(result.data);

                // Update sidebar
                this.updateSidebar();

                this.closeModal('trade-modal');
                console.log('Entry mark added successfully with comprehensive data');
            } else {
                alert('Error adding entry mark: ' + result.message);
            }
        } catch (error) {
            console.error('Error adding entry mark:', error);
            alert('Error adding entry mark: ' + error.message);

            // For now, simulate successful entry for testing using new schema
            console.log('Simulating entry mark for testing...');
            const simulatedData = {
                id: Date.now(),
                symbol: symbol,
                timeframe: timeframe,
                mark_type: 'ENTRY',
                entry_side: entryData.side ? entryData.side.toUpperCase() : 'BUY', // Safe toUpperCase with fallback
                timestamp: entryData.timestamp,
                price: entryData.price,
                quantity: entryData.quantity,
                notes: entryData.notes,
                indicator_snapshot: entryData.indicator_data,
                ohlcv_snapshot: entryData.ohlcv_data,
                status: 'open', // Add status for backward compatibility
                side: entryData.side, // Add side for backward compatibility
                comprehensiveData: comprehensiveData
            };

            this.addMarkToChart(simulatedData);
            this.updateSidebar();
            this.closeModal('trade-modal');
        }
    }

    async confirmExit() {
        const entryId = document.getElementById('trade-entry-select').value;
        const quantity = parseFloat(document.getElementById('trade-exit-quantity').value);
        const notes = document.getElementById('trade-notes').value;

        if (!entryId) {
            alert('Please select an entry to exit');
            return;
        }

        if (!quantity || quantity <= 0) {
            alert('Please enter a valid exit quantity');
            return;
        }

        // Get the selected entry for comprehensive data
        console.log('🔍 Looking for entry with ID:', entryId, 'Type:', typeof entryId);
        console.log('📋 Available marks:', Array.from(this.marks.keys()));

        // Try to get the entry, handling both string and number IDs
        let selectedEntry = this.marks.get(entryId);
        if (!selectedEntry && typeof entryId === 'string') {
            // Try converting to number
            const numericId = parseInt(entryId);
            selectedEntry = this.marks.get(numericId);
            console.log('🔄 Tried numeric ID:', numericId, 'Found:', !!selectedEntry);
        }
        if (!selectedEntry && typeof entryId === 'number') {
            // Try converting to string
            const stringId = entryId.toString();
            selectedEntry = this.marks.get(stringId);
            console.log('🔄 Tried string ID:', stringId, 'Found:', !!selectedEntry);
        }

        if (!selectedEntry) {
            console.error('❌ Selected entry not found. Available entries:');
            this.marks.forEach((mark, id) => {
                console.log(`   - ID: ${id} (${typeof id}), Type: ${mark.mark_type}, Side: ${mark.entry_side || mark.side}`);
            });
            alert('Selected entry not found. Please try selecting an entry again.');
            return;
        }

        console.log('✅ Found selected entry:', selectedEntry);

        // Validate stored trade data
        if (!this.currentTradeData) {
            console.error('❌ No stored trade data available for exit');
            alert('Error: Trade data not available. Please close and reopen the modal.');
            return;
        }

        // Get comprehensive market data using stored data
        const symbol = this.currentTradeData.symbol;
        const timeframe = this.currentTradeData.timeframe;

        // Validate timestamp
        if (!this.currentTradeData.timestamp || isNaN(this.currentTradeData.timestamp)) {
            console.error('❌ Invalid timestamp in stored trade data:', this.currentTradeData.timestamp);
            alert('Invalid timestamp data. Please try clicking on the chart again.');
            return;
        }

        // Validate price
        if (!this.currentTradeData.price || isNaN(this.currentTradeData.price)) {
            console.error('❌ Invalid price in stored trade data:', this.currentTradeData.price);
            alert('Invalid price data. Please try clicking on the chart again.');
            return;
        }

        console.log('✅ Exit mark - using stored trade data:', this.currentTradeData);
        console.log('Exit mark - extracting OHLCV data for timestamp:', this.currentTradeData.timestamp);
        const ohlcvData = await this.getOHLCVData(this.currentTradeData.timestamp);
        console.log('Exit mark - OHLCV data extracted:', ohlcvData);

        if (!ohlcvData) {
            console.warn('⚠️  No OHLCV data available for exit mark');
        }

        console.log('Exit mark - extracting indicator data for timestamp:', this.currentTradeData.timestamp);
        const indicatorData = await this.getIndicatorData(this.currentTradeData.timestamp);
        console.log('Exit mark - Indicator data extracted:', indicatorData);

        if (!indicatorData) {
            console.warn('⚠️  No indicator data available for exit mark');
        }

        // Validate that we have at least some market data
        if (!ohlcvData && !indicatorData) {
            const proceed = confirm(
                'No market data (OHLCV or indicators) could be extracted for this exit mark. ' +
                'This may be due to missing chart data at the selected timestamp. ' +
                'Do you want to proceed anyway?'
            );
            if (!proceed) {
                return;
            }
        }

        const exitData = {
            entry_id: parseInt(entryId),
            timestamp: this.currentTradeData.timestamp,
            price: this.currentTradeData.price,
            quantity: quantity,
            notes: notes,
            ohlcv_data: ohlcvData,
            indicator_data: indicatorData
        };

        console.log('Exit data being sent to server:', exitData);

        // Handle both old and new schema formats for P&L calculation
        const entryPrice = selectedEntry.entry_price || selectedEntry.price || 0;
        let side = selectedEntry.side || (selectedEntry.entry_side ? selectedEntry.entry_side.toLowerCase() : null) || 'buy';

        // Ensure side is a valid string
        if (!side || typeof side !== 'string') {
            side = 'buy'; // Default fallback
        }

        const entryTimestamp = selectedEntry.entry_timestamp || selectedEntry.timestamp;

        // Calculate P&L using stored trade data
        let priceDiff = this.currentTradeData.price - entryPrice;
        if (side === 'sell') {
            priceDiff = -priceDiff;
        }
        const profitPct = (priceDiff / entryPrice) * 100;

        // Store comprehensive data for local use with enhanced validation
        const comprehensiveData = {
            symbol: symbol,
            timeframe: timeframe,
            entry: selectedEntry.comprehensiveData?.entry || {
                timestamp: new Date(entryTimestamp * 1000).toISOString(),
                entry_side: side && typeof side === 'string' ? side.charAt(0).toUpperCase() + side.slice(1) : 'Buy', // Safe toUpperCase
                price: entryPrice,
                ohlcv: this.parseOHLCVData(selectedEntry.entry_ohlcv_data || selectedEntry.ohlcv_snapshot),
                indicators: this.parseIndicatorData(selectedEntry.entry_indicator_data || selectedEntry.indicator_snapshot)
            },
            exit: {
                timestamp: new Date(this.currentTradeData.timestamp * 1000).toISOString(),
                unix_timestamp: this.currentTradeData.timestamp,
                price: this.currentTradeData.price,
                ohlcv: ohlcvData,
                indicators: indicatorData,
                data_quality: {
                    ohlcv_available: !!ohlcvData,
                    indicators_available: !!indicatorData,
                    timestamp_valid: !isNaN(this.currentTradeData.timestamp),
                    price_valid: !isNaN(this.currentTradeData.price)
                }
            },
            pnl: {
                absolute: priceDiff * quantity,
                percentage: profitPct,
                quantity: quantity,
                entry_price: entryPrice,
                exit_price: this.currentClickData.price,
                side: side
            }
        };

        console.log('Comprehensive exit data with validation:', comprehensiveData);

        try {
            const response = await fetch('/api/v1/marks/exit', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(exitData)
            });

            if (!response.ok) {
                const errorText = await response.text();
                console.error('Server error response:', errorText);
                throw new Error(`Server error: ${response.status} - ${errorText}`);
            }

            const result = await response.json();
            console.log('Exit mark API response:', result);

            if (result.success) {
                // Store comprehensive data with the mark
                result.data.comprehensiveData = comprehensiveData;

                // Update mark on chart
                this.updateMarkOnChart(result.data);

                // Update sidebar
                this.updateSidebar();

                this.closeModal('trade-modal');
                console.log('Exit mark added successfully with comprehensive data');

                // Log data extraction success
                console.log('✅ Exit mark data extraction completed:', {
                    ohlcv_extracted: !!ohlcvData,
                    indicators_extracted: !!indicatorData,
                    timestamp_valid: !isNaN(this.currentClickData.time),
                    price_valid: !isNaN(this.currentClickData.price)
                });
            } else {
                console.error('Exit mark creation failed:', result);
                alert('Error adding exit mark: ' + (result.message || 'Unknown error'));
            }
        } catch (error) {
            console.error('Error adding exit mark:', error);
            alert('Error adding exit mark: ' + error.message);

            // Log data extraction failure details
            console.error('❌ Exit mark data extraction failed:', {
                error: error.message,
                ohlcv_data: ohlcvData,
                indicator_data: indicatorData,
                timestamp: this.currentClickData.time,
                price: this.currentClickData.price
            });
        }
    }

    closeModal(modalId) {
        const modal = document.getElementById(modalId);
        if (modal) {
            modal.style.display = 'none';
        }
        this.currentClickData = null;
        this.currentModalType = null; // Clear modal type when closing
        this.currentTradeData = null; // Clear stored trade data when closing
        console.log('🔒 Modal closed and data cleared');
    }

    getCurrentSymbol() {
        // Get current symbol from the chart or global state
        return window.currentSymbol || 'BTCUSDT';
    }

    getCurrentTimeframe() {
        // Get current timeframe from the chart or global state
        return window.currentTimeframe || '15m';
    }

    getCandlestickAtTime(targetTime) {
        console.log('🔍 getCandlestickAtTime called with targetTime:', targetTime);

        // Find the exact candlestick for the given timestamp
        // Try multiple ways to access the chart data
        let chartData = null;

        // Debug current data state
        console.log('🔍 Chart currentData type:', typeof this.chart.currentData);
        console.log('🔍 Chart currentData length:', this.chart.currentData?.length);
        console.log('🔍 Chart currentData sample:', this.chart.currentData?.[0]);

        if (this.chart.currentData && this.chart.currentData.length > 0) {
            chartData = this.chart.currentData;
            console.log('✅ Using chart.currentData with', chartData.length, 'candles');
        } else if (this.chart.dataFeed && this.chart.dataFeed.data) {
            chartData = this.chart.dataFeed.data;
            console.log('✅ Using chart.dataFeed.data with', chartData.length, 'candles');
        } else if (this.chart.dataFeed && typeof this.chart.dataFeed.getData === 'function') {
            chartData = this.chart.dataFeed.getData();
            console.log('✅ Using chart.dataFeed.getData() with', chartData?.length, 'candles');
        } else {
            // Try to get data from TradingView series directly
            console.log('🔍 Trying to get data from series...');
            chartData = this.tryGetDataFromSeries();

            // If still no data, try chart API
            if (!chartData || chartData.length === 0) {
                console.log('🔍 Trying chart API exploration...');
                this.tryGetDataFromChartAPI(); // This is for exploration, doesn't return data yet
            }
        }

        if (!chartData || chartData.length === 0) {
            console.warn('❌ No chart data available in getCandlestickAtTime');
            console.log('🔍 Chart object keys:', Object.keys(this.chart));
            console.log('🔍 currentData details:', {
                exists: !!this.chart.currentData,
                type: typeof this.chart.currentData,
                length: this.chart.currentData?.length,
                isArray: Array.isArray(this.chart.currentData)
            });
            return null;
        }

        console.log(`✅ Found ${chartData.length} candles in chart data`);

        // Find the candlestick that matches the target time
        // TradingView time is usually in seconds, so we need to find the exact match
        let bestMatch = null;
        let minDifference = Infinity;

        for (const candle of chartData) {
            const timeDiff = Math.abs(candle.time - targetTime);
            if (timeDiff < minDifference) {
                minDifference = timeDiff;
                bestMatch = candle;
            }

            // If we find an exact match, use it
            if (timeDiff === 0) {
                break;
            }
        }

        if (bestMatch && minDifference < 3600) { // Within 1 hour tolerance
            console.log(`✅ Found candlestick match with ${minDifference}s difference:`, bestMatch);
            return {
                time: bestMatch.time,
                open: bestMatch.open,
                high: bestMatch.high,
                low: bestMatch.low,
                close: bestMatch.close,
                volume: bestMatch.volume || 0
            };
        }

        // If no good match found, use the latest candle
        const latestCandle = chartData[chartData.length - 1];
        if (latestCandle) {
            console.log('Using latest candle as fallback:', latestCandle);
            return {
                time: latestCandle.time,
                open: latestCandle.open,
                high: latestCandle.high,
                low: latestCandle.low,
                close: latestCandle.close,
                volume: latestCandle.volume || 0
            };
        }

        return null;
    }

    // Deep search using TradingView Lightweight Charts official API methods
    tryGetDataFromSeries() {
        console.log('=== Deep TradingView Data Search ===');
        console.log('Candlestick series object:', this.chart.candlestickSeries);

        if (!this.chart.candlestickSeries) {
            console.log('No candlestick series available');
            return null;
        }

        try {
            // Method 1: Try to access data through TradingView's internal data model
            // Based on TradingView source code, series data is stored in internal models
            console.log('\n--- Method 1: Internal Data Model ---');
            const series = this.chart.candlestickSeries;

            // TradingView stores data in _internal._model._data or similar structures
            if (series._internal) {
                console.log('Found _internal:', series._internal);

                if (series._internal._model) {
                    console.log('Found _internal._model:', series._internal._model);

                    // Look for data in the model
                    const model = series._internal._model;
                    const modelProps = ['_data', 'data', '_bars', 'bars', '_items', 'items'];

                    for (const prop of modelProps) {
                        if (model[prop] && Array.isArray(model[prop]) && model[prop].length > 0) {
                            console.log(`Found data in _internal._model.${prop}:`, model[prop].length, 'items');
                            console.log('Sample data:', model[prop].slice(0, 2));
                            return this.convertTradingViewData(model[prop]);
                        }
                    }
                }

                // Try other internal properties
                const internalProps = ['_dataSource', '_series', '_seriesModel', '_bars', '_data'];
                for (const prop of internalProps) {
                    if (series._internal[prop]) {
                        console.log(`Found _internal.${prop}:`, typeof series._internal[prop]);

                        if (Array.isArray(series._internal[prop]) && series._internal[prop].length > 0) {
                            console.log(`Data found in _internal.${prop}:`, series._internal[prop].length, 'items');
                            return this.convertTradingViewData(series._internal[prop]);
                        }

                        // If it's an object, look deeper
                        if (typeof series._internal[prop] === 'object' && series._internal[prop] !== null) {
                            const deepData = this.searchObjectForData(series._internal[prop], `_internal.${prop}`);
                            if (deepData) return deepData;
                        }
                    }
                }
            }

            // Method 2: Try to get data through chart API using visible range
            console.log('\n--- Method 2: Chart API with Visible Range ---');
            if (this.chart.chart) {
                const timeScale = this.chart.chart.timeScale();
                const visibleRange = timeScale.getVisibleRange();
                console.log('Visible range:', visibleRange);

                if (visibleRange && visibleRange.from && visibleRange.to) {
                    // Try to extract data for the visible range
                    const data = this.extractDataFromVisibleRange(visibleRange);
                    if (data) return data;
                }
            }

            // Method 3: Deep recursive search through all properties
            console.log('\n--- Method 3: Deep Recursive Search ---');
            const foundData = this.deepSearchForCandlestickData(series, 'series', 0, 5);
            if (foundData) return foundData;

            // Method 4: Try to intercept data from chart updates
            console.log('\n--- Method 4: Chart Update Interception ---');
            const interceptedData = this.tryInterceptChartData();
            if (interceptedData) return interceptedData;

        } catch (error) {
            console.log('Error in deep data search:', error);
        }

        console.log('❌ No candlestick data found through any method');
        return null;
    }

    // Convert TradingView internal data format to standard OHLCV format
    convertTradingViewData(rawData) {
        console.log('Converting TradingView data format...');

        if (!Array.isArray(rawData) || rawData.length === 0) {
            return null;
        }

        // TradingView data might be in different formats
        const sample = rawData[0];
        console.log('Sample raw data item:', sample);

        // Try to convert based on the structure
        const convertedData = rawData.map(item => {
            // Handle different possible formats
            if (item.time !== undefined && item.open !== undefined) {
                // Already in correct format
                return item;
            } else if (item.t !== undefined && item.o !== undefined) {
                // Abbreviated format
                return {
                    time: item.t,
                    open: item.o,
                    high: item.h,
                    low: item.l,
                    close: item.c,
                    volume: item.v || 0
                };
            } else if (Array.isArray(item) && item.length >= 5) {
                // Array format [time, open, high, low, close, volume?]
                return {
                    time: item[0],
                    open: item[1],
                    high: item[2],
                    low: item[3],
                    close: item[4],
                    volume: item[5] || 0
                };
            }

            return null;
        }).filter(item => item !== null);

        console.log(`Converted ${convertedData.length} data items`);
        return convertedData.length > 0 ? convertedData : null;
    }

    // Deep recursive search for candlestick data
    deepSearchForCandlestickData(obj, path, depth, maxDepth) {
        if (depth >= maxDepth || !obj || typeof obj !== 'object') {
            return null;
        }

        // Check if this object looks like candlestick data
        if (Array.isArray(obj) && obj.length > 0) {
            const sample = obj[0];
            if (this.looksLikeCandlestickData(sample)) {
                console.log(`Found potential candlestick data at ${path}:`, obj.length, 'items');
                console.log('Sample:', sample);
                return this.convertTradingViewData(obj);
            }
        }

        // Recursively search object properties
        try {
            const keys = Object.keys(obj);
            for (const key of keys) {
                if (obj[key] && typeof obj[key] === 'object') {
                    const result = this.deepSearchForCandlestickData(obj[key], `${path}.${key}`, depth + 1, maxDepth);
                    if (result) return result;
                }
            }
        } catch (error) {
            // Ignore errors accessing properties
        }

        return null;
    }

    // Check if an object looks like candlestick data
    looksLikeCandlestickData(item) {
        if (!item || typeof item !== 'object') return false;

        // Check for standard OHLCV properties
        const hasStandardProps = item.time !== undefined &&
                                 item.open !== undefined &&
                                 item.high !== undefined &&
                                 item.low !== undefined &&
                                 item.close !== undefined;

        // Check for abbreviated properties
        const hasAbbrevProps = item.t !== undefined &&
                              item.o !== undefined &&
                              item.h !== undefined &&
                              item.l !== undefined &&
                              item.c !== undefined;

        // Check for array format
        const isArrayFormat = Array.isArray(item) && item.length >= 5;

        return hasStandardProps || hasAbbrevProps || isArrayFormat;
    }

    // Search object for data arrays
    searchObjectForData(obj, path) {
        if (!obj || typeof obj !== 'object') return null;

        const dataProps = ['data', '_data', 'items', '_items', 'values', '_values', 'bars', '_bars', 'points', '_points'];

        for (const prop of dataProps) {
            if (obj[prop] && Array.isArray(obj[prop]) && obj[prop].length > 0) {
                console.log(`Found data array at ${path}.${prop}:`, obj[prop].length, 'items');
                const converted = this.convertTradingViewData(obj[prop]);
                if (converted) return converted;
            }
        }

        return null;
    }

    // Try to intercept data from chart updates
    tryInterceptChartData() {
        console.log('Attempting to intercept chart data...');

        // This is a more advanced technique - try to access the chart's data through update mechanisms
        if (this.chart.dataFeed && this.chart.dataFeed.data && this.chart.dataFeed.data.length > 0) {
            console.log('Found data in dataFeed:', this.chart.dataFeed.data.length, 'items');
            return this.chart.dataFeed.data;
        }

        return null;
    }

    // Extract data from visible range using TradingView API
    extractDataFromVisibleRange(visibleRange) {
        console.log('Extracting data from visible range...');

        try {
            const timeScale = this.chart.chart.timeScale();
            const priceScale = this.chart.chart.priceScale('right');

            if (!timeScale || !priceScale) {
                console.log('No time or price scale available');
                return null;
            }

            // Try to sample data points across the visible range
            const chartContainer = this.chart.container;
            if (!chartContainer) return null;

            const rect = chartContainer.getBoundingClientRect();
            const width = rect.width;
            const samplePoints = Math.min(100, width / 10); // Sample every 10 pixels

            const extractedData = [];

            for (let i = 0; i < samplePoints; i++) {
                const x = (i / samplePoints) * width;

                try {
                    const time = timeScale.coordinateToTime(x);
                    if (time) {
                        // Try to get OHLCV data for this time point
                        const dataPoint = this.getDataPointAtTime(time);
                        if (dataPoint) {
                            extractedData.push(dataPoint);
                        }
                    }
                } catch (error) {
                    // Continue sampling other points
                }
            }

            if (extractedData.length > 0) {
                console.log(`Extracted ${extractedData.length} data points from visible range`);
                return extractedData;
            }

        } catch (error) {
            console.log('Error extracting data from visible range:', error);
        }

        return null;
    }

    // Try to get a data point at a specific time
    getDataPointAtTime(time) {
        // This is a placeholder - in a real implementation, we'd need to
        // access TradingView's internal data structures or use their API
        // to get the actual OHLCV data for a specific timestamp

        // For now, we'll return null and rely on other methods
        return null;
    }

    async getOHLCVData(timestamp) {
        console.log('🔍 Getting OHLCV data for timestamp:', timestamp);
        console.log('🔍 Current click data available:', !!this.currentClickData);
        console.log('🔍 Current click candlestick data:', this.currentClickData?.candlestickData);

        // First priority: Use candlestick data from the current click if available
        if (this.currentClickData && this.currentClickData.candlestickData) {
            console.log('✅ Using candlestick data from current click:', this.currentClickData.candlestickData);
            return this.currentClickData.candlestickData;
        }

        // Fallback: check for old property name (candlestick) for backward compatibility
        if (this.currentClickData && this.currentClickData.candlestick) {
            console.log('✅ Using candlestick data from current click (legacy property):', this.currentClickData.candlestick);
            return this.currentClickData.candlestick;
        }

        // Second priority: Try to find the candlestick using the timestamp
        console.log('🔍 Attempting to get candlestick by timestamp...');
        const candlestickData = this.getCandlestickAtTime(timestamp);
        if (candlestickData) {
            console.log('✅ Found candlestick data by timestamp:', candlestickData);
            return candlestickData;
        }

        // Third priority: Try to get data from series
        console.log('🔍 Attempting to get data from series...');
        const seriesData = this.tryGetDataFromSeries();
        console.log('🔍 Series data result:', seriesData ? `${seriesData.length} items` : 'null');

        if (seriesData && seriesData.length > 0) {
            // Find the closest data point to the timestamp
            const targetTime = Math.floor(timestamp);
            let closestData = null;
            let minTimeDiff = Infinity;

            console.log('🔍 Searching for closest data point to timestamp:', targetTime);
            for (const dataPoint of seriesData) {
                const dataTime = Math.floor(dataPoint.time || dataPoint.t || 0);
                const timeDiff = Math.abs(dataTime - targetTime);

                if (timeDiff < minTimeDiff) {
                    minTimeDiff = timeDiff;
                    closestData = dataPoint;
                }
            }

            if (closestData) {
                console.log('✅ Found closest OHLCV data from series (diff:', minTimeDiff, 's):', closestData);
                return this.normalizeOHLCVData(closestData);
            }
        }

        // Fourth priority: Try to get OHLCV data from backend API
        console.log('⚠️  No frontend OHLCV data available, attempting to fetch from backend...');
        try {
            const backendOHLCV = await this.fetchOHLCVFromBackend(timestamp);
            if (backendOHLCV) {
                console.log('✅ Using backend OHLCV data:', backendOHLCV);
                return backendOHLCV;
            }
        } catch (error) {
            console.warn('❌ Failed to fetch OHLCV from backend:', error);
        }

        // Don't create synthetic data - return null if we can't get real candlestick data
        console.warn('❌ No candlestick data available from any source');
        console.log('🔍 Debug info:');
        console.log('   - Chart object:', !!this.chart);
        console.log('   - Chart currentData:', !!this.chart?.currentData);
        console.log('   - Chart dataFeed:', !!this.chart?.dataFeed);
        console.log('   - Chart dataFeed.data:', !!this.chart?.dataFeed?.data);
        return null;
    }

    // Normalize OHLCV data to ensure consistent format
    normalizeOHLCVData(data) {
        if (!data) return null;

        // Handle different data formats
        let normalized = {};

        if (data.time !== undefined && data.open !== undefined) {
            // Standard format
            normalized = {
                time: data.time,
                open: parseFloat(data.open),
                high: parseFloat(data.high),
                low: parseFloat(data.low),
                close: parseFloat(data.close),
                volume: parseFloat(data.volume || 0)
            };
        } else if (data.t !== undefined && data.o !== undefined) {
            // Abbreviated format
            normalized = {
                time: data.t,
                open: parseFloat(data.o),
                high: parseFloat(data.h),
                low: parseFloat(data.l),
                close: parseFloat(data.c),
                volume: parseFloat(data.v || 0)
            };
        } else if (Array.isArray(data) && data.length >= 5) {
            // Array format [time, open, high, low, close, volume?]
            normalized = {
                time: data[0],
                open: parseFloat(data[1]),
                high: parseFloat(data[2]),
                low: parseFloat(data[3]),
                close: parseFloat(data[4]),
                volume: parseFloat(data[5] || 0)
            };
        } else {
            console.warn('Unknown OHLCV data format:', data);
            return null;
        }

        // Validate the normalized data
        if (isNaN(normalized.open) || isNaN(normalized.high) ||
            isNaN(normalized.low) || isNaN(normalized.close)) {
            console.warn('Invalid OHLCV data values:', normalized);
            return null;
        }

        console.log('✅ Normalized OHLCV data:', normalized);
        return normalized;
    }

    // Helper method to safely parse OHLCV data from JSON strings
    parseOHLCVData(data) {
        if (!data) return null;

        try {
            if (typeof data === 'string') {
                return JSON.parse(data);
            }
            return data;
        } catch (error) {
            console.warn('Error parsing OHLCV data:', error);
            return null;
        }
    }

    // Helper method to safely parse indicator data from JSON strings
    parseIndicatorData(data) {
        if (!data) return null;

        try {
            if (typeof data === 'string') {
                return JSON.parse(data);
            }
            return data;
        } catch (error) {
            console.warn('Error parsing indicator data:', error);
            return null;
        }
    }

    // Try to access data through TradingView chart API
    tryGetDataFromChartAPI() {
        console.log('Attempting to get data from TradingView chart API...');

        if (!this.chart.chart) {
            console.log('No chart API available');
            return null;
        }

        try {
            // Try to get visible range and data
            const timeScale = this.chart.chart.timeScale();
            const visibleRange = timeScale.getVisibleRange();
            console.log('Visible range:', visibleRange);

            // Try to get series data through different methods
            if (this.chart.candlestickSeries) {
                console.log('Trying to access series data through chart API...');

                // Try to get data at specific coordinates
                const chartContainer = this.chart.container;
                if (chartContainer) {
                    const rect = chartContainer.getBoundingClientRect();
                    const centerX = rect.width / 2;

                    try {
                        const time = timeScale.coordinateToTime(centerX);
                        console.log('Center time:', time);

                        // This might give us access to the data at that time
                        const priceScale = this.chart.chart.priceScale('right');
                        console.log('Price scale:', priceScale);

                    } catch (error) {
                        console.log('Error getting time/price from coordinates:', error);
                    }
                }
            }

        } catch (error) {
            console.log('Error accessing chart API:', error);
        }

        return null;
    }

    async getIndicatorData(timestamp) {
        // Get indicator data if available
        const indicators = {};
        console.log('🔍 getIndicatorData called for timestamp:', timestamp);

        try {
            // First try to get data from indicatorsManager.indicatorData (calculated values)
            if (window.indicatorsManager && window.indicatorsManager.indicatorData) {
                const indicatorData = window.indicatorsManager.indicatorData;
                console.log('📊 Available indicator data:', Object.keys(indicatorData));

                if (Object.keys(indicatorData).length > 0) {
                    // Extract the latest values from the indicator arrays
                    const latestIndicators = {};

                    // Get EMA values (latest from arrays)
                    if (indicatorData.ema) {
                        const emaData = {};
                        Object.keys(indicatorData.ema).forEach(key => {
                            const values = indicatorData.ema[key];
                            if (values && values.length > 0) {
                                emaData[key] = values[values.length - 1]; // Get latest value
                            }
                        });
                        if (Object.keys(emaData).length > 0) {
                            latestIndicators.ema = emaData;
                        }
                    }

                    // Get RSI values (latest from arrays)
                    if (indicatorData.rsi) {
                        const rsiData = {};
                        Object.keys(indicatorData.rsi).forEach(key => {
                            const values = indicatorData.rsi[key];
                            if (values && values.length > 0) {
                                rsiData[key] = values[values.length - 1]; // Get latest value
                            }
                        });
                        if (Object.keys(rsiData).length > 0) {
                            latestIndicators.rsi = rsiData;
                        }
                    }

                    // Get MACD values (latest from arrays)
                    if (indicatorData.macd) {
                        const macdData = {};
                        if (indicatorData.macd.macd && indicatorData.macd.macd.length > 0) {
                            macdData.macd = indicatorData.macd.macd[indicatorData.macd.macd.length - 1];
                        }
                        if (indicatorData.macd.signal && indicatorData.macd.signal.length > 0) {
                            macdData.signal = indicatorData.macd.signal[indicatorData.macd.signal.length - 1];
                        }
                        if (indicatorData.macd.histogram && indicatorData.macd.histogram.length > 0) {
                            macdData.histogram = indicatorData.macd.histogram[indicatorData.macd.histogram.length - 1];
                        }
                        if (Object.keys(macdData).length > 0) {
                            latestIndicators.macd = macdData;
                        }
                    }

                    // Get Bollinger Bands values (latest from arrays)
                    if (indicatorData.bollinger_bands) {
                        const bbData = {};
                        if (indicatorData.bollinger_bands.upper && indicatorData.bollinger_bands.upper.length > 0) {
                            bbData.upper = indicatorData.bollinger_bands.upper[indicatorData.bollinger_bands.upper.length - 1];
                        }
                        if (indicatorData.bollinger_bands.middle && indicatorData.bollinger_bands.middle.length > 0) {
                            bbData.middle = indicatorData.bollinger_bands.middle[indicatorData.bollinger_bands.middle.length - 1];
                        }
                        if (indicatorData.bollinger_bands.lower && indicatorData.bollinger_bands.lower.length > 0) {
                            bbData.lower = indicatorData.bollinger_bands.lower[indicatorData.bollinger_bands.lower.length - 1];
                        }
                        if (Object.keys(bbData).length > 0) {
                            latestIndicators.bollinger_bands = bbData;
                        }
                    }

                    if (Object.keys(latestIndicators).length > 0) {
                        console.log('✅ Using latest calculated indicator data:', latestIndicators);
                        return latestIndicators;
                    }
                }
            }

            // Fallback: try to get data from indicator series if available
            if (window.indicatorsManager && window.indicatorsManager.indicatorSeries) {
                const series = window.indicatorsManager.indicatorSeries;
                console.log('📊 Available indicator series:', Object.keys(series));

                // EMA indicators
                const emaData = {};
                Object.keys(series).forEach(key => {
                    if (key.includes('ema') || key.includes('EMA')) {
                        const seriesData = series[key];
                        if (seriesData && seriesData.data) {
                            const latestValue = seriesData.data[seriesData.data.length - 1];
                            if (latestValue) {
                                emaData[key] = latestValue.value || latestValue;
                            }
                        }
                    }
                });
                if (Object.keys(emaData).length > 0) {
                    indicators.ema = emaData;
                }

                // RSI indicators
                const rsiData = {};
                Object.keys(series).forEach(key => {
                    if (key.includes('rsi') || key.includes('RSI')) {
                        const seriesData = series[key];
                        if (seriesData && seriesData.data) {
                            const latestValue = seriesData.data[seriesData.data.length - 1];
                            if (latestValue) {
                                rsiData[key] = latestValue.value || latestValue;
                            }
                        }
                    }
                });
                if (Object.keys(rsiData).length > 0) {
                    indicators.rsi = rsiData;
                }

                // MACD indicators
                const macdData = {};
                Object.keys(series).forEach(key => {
                    if (key.includes('macd') || key.includes('MACD')) {
                        const seriesData = series[key];
                        if (seriesData && seriesData.data) {
                            const latestValue = seriesData.data[seriesData.data.length - 1];
                            if (latestValue) {
                                macdData[key] = latestValue.value || latestValue;
                            }
                        }
                    }
                });
                if (Object.keys(macdData).length > 0) {
                    indicators.macd = macdData;
                }

                // Bollinger Bands
                const bbData = {};
                Object.keys(series).forEach(key => {
                    if (key.includes('bollinger') || key.includes('bb') || key.includes('BB')) {
                        const seriesData = series[key];
                        if (seriesData && seriesData.data) {
                            const latestValue = seriesData.data[seriesData.data.length - 1];
                            if (latestValue) {
                                bbData[key] = latestValue.value || latestValue;
                            }
                        }
                    }
                });
                if (Object.keys(bbData).length > 0) {
                    indicators.bollinger_bands = bbData;
                }
            }

            // Try to get indicator data from the backend API if no frontend indicators available
            if (Object.keys(indicators).length === 0) {
                console.log('⚠️  No frontend indicators available, attempting to fetch from backend...');
                console.log('🔍 Indicators manager state:', {
                    exists: !!window.indicatorsManager,
                    hasSeries: !!window.indicatorsManager?.indicatorSeries,
                    seriesCount: window.indicatorsManager?.indicatorSeries ? Object.keys(window.indicatorsManager.indicatorSeries).length : 0
                });

                try {
                    const backendIndicators = await this.fetchIndicatorsFromBackend(timestamp);
                    if (backendIndicators && Object.keys(backendIndicators).length > 0) {
                        Object.assign(indicators, backendIndicators);
                        console.log('✅ Using backend indicator data:', indicators);
                    } else {
                        console.log('❌ No indicator data available from backend either');
                        // Check if we should provide sample data for testing
                        console.log('💡 Consider adding indicators to the chart or enabling backend indicator API');
                        return null;
                    }
                } catch (error) {
                    console.warn('Failed to fetch indicators from backend:', error);
                    return null;
                }
            } else {
                console.log('✅ Using real frontend indicator data:', indicators);
            }

        } catch (error) {
            console.warn('Error getting indicator data:', error);
        }

        return indicators;
    }

    async fetchIndicatorsFromBackend(timestamp) {
        /**
         * Fetch indicator data from backend API for the given timestamp
         */
        try {
            const symbol = this.getCurrentSymbol();
            const timeframe = this.getCurrentTimeframe();

            // Convert timestamp to ISO format for API
            const isoTimestamp = new Date(timestamp * 1000).toISOString();

            const response = await fetch(`/api/v1/indicators/data?symbol=${symbol}&timeframe=${timeframe}&timestamp=${isoTimestamp}`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                }
            });

            if (response.ok) {
                const data = await response.json();
                if (data.success && data.data) {
                    console.log('✅ Fetched indicators from backend:', data.data);
                    return data.data;
                }
            } else {
                console.warn('Backend indicators API returned:', response.status);
            }
        } catch (error) {
            console.warn('Error fetching indicators from backend:', error);
        }

        return null;
    }

    async fetchOHLCVFromBackend(timestamp) {
        /**
         * Fetch OHLCV data from backend API for the given timestamp
         */
        try {
            const symbol = this.getCurrentSymbol();
            const timeframe = this.getCurrentTimeframe();

            // Convert timestamp to ISO format for API
            const targetDate = new Date(timestamp * 1000);
            const startTime = new Date(targetDate.getTime() - 60 * 60 * 1000).toISOString(); // 1 hour before
            const endTime = new Date(targetDate.getTime() + 60 * 60 * 1000).toISOString(); // 1 hour after

            const response = await fetch(`/api/v1/ohlcv/data?symbol=${symbol}&timeframe=${timeframe}&start_time=${startTime}&end_time=${endTime}&limit=10`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                }
            });

            if (response.ok) {
                const data = await response.json();
                if (data.success && data.data && data.data.length > 0) {
                    // Find the closest OHLCV data point to our timestamp
                    let closestData = null;
                    let minTimeDiff = Infinity;

                    for (const ohlcv of data.data) {
                        const ohlcvTime = new Date(ohlcv.timestamp).getTime() / 1000;
                        const timeDiff = Math.abs(ohlcvTime - timestamp);

                        if (timeDiff < minTimeDiff) {
                            minTimeDiff = timeDiff;
                            closestData = {
                                time: ohlcvTime,
                                open: parseFloat(ohlcv.open),
                                high: parseFloat(ohlcv.high),
                                low: parseFloat(ohlcv.low),
                                close: parseFloat(ohlcv.close),
                                volume: parseFloat(ohlcv.volume)
                            };
                        }
                    }

                    if (closestData && minTimeDiff < 3600) { // Within 1 hour
                        console.log('✅ Fetched OHLCV from backend:', closestData);
                        return closestData;
                    }
                }
            } else {
                console.warn('Backend OHLCV API returned:', response.status);
            }
        } catch (error) {
            console.warn('Error fetching OHLCV from backend:', error);
        }

        return null;
    }

    setElementText(elementId, text) {
        const element = document.getElementById(elementId);
        if (element) {
            element.textContent = text;
        }
    }

    setElementTextWithHover(elementId, text, tooltipContent) {
        const element = document.getElementById(elementId);
        if (element) {
            element.innerHTML = `
                <span class="hover-details">
                    ${text}
                    <div class="tooltip">
                        <div class="tooltip-content">
                            ${tooltipContent}
                        </div>
                    </div>
                </span>
            `;
        }
    }

    createPriceTooltip(price) {
        return `
            <div class="tooltip-row">
                <span class="tooltip-label">Exact Price:</span>
                <span class="tooltip-value">$${price.toFixed(8)}</span>
            </div>
            <div class="tooltip-row">
                <span class="tooltip-label">Rounded:</span>
                <span class="tooltip-value">$${price.toFixed(2)}</span>
            </div>
            <div class="tooltip-row">
                <span class="tooltip-label">Timestamp:</span>
                <span class="tooltip-value">${this.currentClickData.time}</span>
            </div>
        `;
    }

    createOHLCVTooltip(ohlcvData) {
        if (!ohlcvData) return 'No OHLCV data available';

        const range = ohlcvData.high - ohlcvData.low;
        const bodySize = Math.abs(ohlcvData.close - ohlcvData.open);
        const upperWick = ohlcvData.high - Math.max(ohlcvData.open, ohlcvData.close);
        const lowerWick = Math.min(ohlcvData.open, ohlcvData.close) - ohlcvData.low;

        return `
            <div class="tooltip-row">
                <span class="tooltip-label">Range:</span>
                <span class="tooltip-value">$${range.toFixed(2)}</span>
            </div>
            <div class="tooltip-row">
                <span class="tooltip-label">Body Size:</span>
                <span class="tooltip-value">$${bodySize.toFixed(2)}</span>
            </div>
            <div class="tooltip-row">
                <span class="tooltip-label">Upper Wick:</span>
                <span class="tooltip-value">$${upperWick.toFixed(2)}</span>
            </div>
            <div class="tooltip-row">
                <span class="tooltip-label">Lower Wick:</span>
                <span class="tooltip-value">$${lowerWick.toFixed(2)}</span>
            </div>
            <div class="tooltip-row">
                <span class="tooltip-label">Volume:</span>
                <span class="tooltip-value">${ohlcvData.volume.toLocaleString()}</span>
            </div>
        `;
    }

    populateOHLCVData(containerId, ohlcvData) {
        const container = document.getElementById(containerId);
        if (!container) return;

        if (!ohlcvData) {
            container.innerHTML = '<div class="data-row"><span class="data-label">No OHLCV data available</span></div>';
            return;
        }

        const tooltipContent = this.createOHLCVTooltip(ohlcvData);

        container.innerHTML = `
            <div class="data-row">
                <span class="data-label">Open:</span>
                <span class="data-value hover-details">
                    $${ohlcvData.open.toFixed(2)}
                    <div class="tooltip">
                        <div class="tooltip-content">
                            <div class="tooltip-row">
                                <span class="tooltip-label">Exact:</span>
                                <span class="tooltip-value">$${ohlcvData.open.toFixed(8)}</span>
                            </div>
                        </div>
                    </div>
                </span>
            </div>
            <div class="data-row">
                <span class="data-label">High:</span>
                <span class="data-value hover-details">
                    $${ohlcvData.high.toFixed(2)}
                    <div class="tooltip">
                        <div class="tooltip-content">
                            <div class="tooltip-row">
                                <span class="tooltip-label">Exact:</span>
                                <span class="tooltip-value">$${ohlcvData.high.toFixed(8)}</span>
                            </div>
                        </div>
                    </div>
                </span>
            </div>
            <div class="data-row">
                <span class="data-label">Low:</span>
                <span class="data-value hover-details">
                    $${ohlcvData.low.toFixed(2)}
                    <div class="tooltip">
                        <div class="tooltip-content">
                            <div class="tooltip-row">
                                <span class="tooltip-label">Exact:</span>
                                <span class="tooltip-value">$${ohlcvData.low.toFixed(8)}</span>
                            </div>
                        </div>
                    </div>
                </span>
            </div>
            <div class="data-row">
                <span class="data-label">Close:</span>
                <span class="data-value hover-details">
                    $${ohlcvData.close.toFixed(2)}
                    <div class="tooltip">
                        <div class="tooltip-content">
                            <div class="tooltip-row">
                                <span class="tooltip-label">Exact:</span>
                                <span class="tooltip-value">$${ohlcvData.close.toFixed(8)}</span>
                            </div>
                        </div>
                    </div>
                </span>
            </div>
            <div class="data-row">
                <span class="data-label">Volume:</span>
                <span class="data-value hover-details">
                    ${ohlcvData.volume.toLocaleString()}
                    <div class="tooltip">
                        <div class="tooltip-content">
                            ${tooltipContent}
                        </div>
                    </div>
                </span>
            </div>
        `;
    }

    populateIndicatorData(containerId, indicatorData) {
        const container = document.getElementById(containerId);
        if (!container) return;

        if (!indicatorData || Object.keys(indicatorData).length === 0) {
            container.innerHTML = '<div class="indicator-group"><span class="data-label">No indicator data available</span></div>';
            return;
        }

        let html = '';

        // EMA indicators
        if (indicatorData.ema) {
            html += '<div class="indicator-group">';
            html += '<h5>Exponential Moving Averages</h5>';
            html += '<div class="indicator-values">';
            Object.entries(indicatorData.ema).forEach(([key, value]) => {
                const label = key.replace('_', ' ').toUpperCase();
                html += `
                    <div class="indicator-item">
                        <span class="indicator-label">${label}:</span>
                        <span class="indicator-value">$${value.toFixed(2)}</span>
                    </div>
                `;
            });
            html += '</div></div>';
        }

        // RSI indicators
        if (indicatorData.rsi) {
            html += '<div class="indicator-group">';
            html += '<h5>Relative Strength Index</h5>';
            html += '<div class="indicator-values">';
            Object.entries(indicatorData.rsi).forEach(([key, value]) => {
                const label = key.replace('_', ' ').toUpperCase();
                const colorClass = value > 70 ? 'pnl-negative' : value < 30 ? 'pnl-positive' : '';
                html += `
                    <div class="indicator-item">
                        <span class="indicator-label">${label}:</span>
                        <span class="indicator-value ${colorClass}">${value.toFixed(1)}</span>
                    </div>
                `;
            });
            html += '</div></div>';
        }

        // MACD indicators
        if (indicatorData.macd) {
            html += '<div class="indicator-group">';
            html += '<h5>MACD</h5>';
            html += '<div class="indicator-values">';

            // Display MACD line
            if (indicatorData.macd.macd !== undefined) {
                const macdValue = indicatorData.macd.macd;
                const colorClass = macdValue > 0 ? 'pnl-positive' : macdValue < 0 ? 'pnl-negative' : 'pnl-neutral';
                html += `
                    <div class="indicator-item">
                        <span class="indicator-label">MACD:</span>
                        <span class="indicator-value ${colorClass}">${macdValue.toFixed(3)}</span>
                    </div>
                `;
            }

            // Display Signal line
            if (indicatorData.macd.signal !== undefined) {
                const signalValue = indicatorData.macd.signal;
                const colorClass = signalValue > 0 ? 'pnl-positive' : signalValue < 0 ? 'pnl-negative' : 'pnl-neutral';
                html += `
                    <div class="indicator-item">
                        <span class="indicator-label">Signal:</span>
                        <span class="indicator-value ${colorClass}">${signalValue.toFixed(3)}</span>
                    </div>
                `;
            }

            // Display Histogram
            if (indicatorData.macd.histogram !== undefined) {
                const histogramValue = indicatorData.macd.histogram;
                const colorClass = histogramValue > 0 ? 'pnl-positive' : histogramValue < 0 ? 'pnl-negative' : 'pnl-neutral';
                html += `
                    <div class="indicator-item">
                        <span class="indicator-label">Histogram:</span>
                        <span class="indicator-value ${colorClass}">${histogramValue.toFixed(3)}</span>
                    </div>
                `;
            }

            html += '</div></div>';
        }

        // Bollinger Bands
        if (indicatorData.bollinger_bands) {
            html += '<div class="indicator-group">';
            html += '<h5>Bollinger Bands</h5>';
            html += '<div class="indicator-values">';
            Object.entries(indicatorData.bollinger_bands).forEach(([key, value]) => {
                const label = key.replace('_', ' ').toUpperCase();
                html += `
                    <div class="indicator-item">
                        <span class="indicator-label">${label}:</span>
                        <span class="indicator-value">$${value.toFixed(2)}</span>
                    </div>
                `;
            });
            html += '</div></div>';
        }

        container.innerHTML = html;
    }

    updatePnLCalculation() {
        const entrySelect = document.getElementById('exit-entry-select');
        const quantityInput = document.getElementById('exit-quantity');
        const pnlSection = document.getElementById('exit-pnl-section');
        const pnlContainer = document.getElementById('exit-pnl');

        if (!entrySelect.value || !pnlSection || !pnlContainer) {
            if (pnlSection) pnlSection.style.display = 'none';
            return;
        }

        const selectedEntry = this.marks.get(entrySelect.value);
        if (!selectedEntry) {
            pnlSection.style.display = 'none';
            return;
        }

        // Handle both old and new schema formats
        const entryPrice = selectedEntry.entry_price || selectedEntry.price || 0;
        const side = selectedEntry.side || (selectedEntry.entry_side ? selectedEntry.entry_side.toLowerCase() : null) || 'buy';
        const entryQuantity = selectedEntry.quantity || 1; // Default quantity for new schema

        const exitPrice = this.currentClickData.price;
        const quantity = parseFloat(quantityInput.value) || entryQuantity;

        // Calculate P&L
        let priceDiff = exitPrice - entryPrice;
        if (side === 'sell') {
            priceDiff = -priceDiff; // Reverse for short positions
        }

        const absolutePnL = priceDiff * quantity;
        const percentagePnL = (priceDiff / entryPrice) * 100;

        // Determine color class
        const colorClass = absolutePnL > 0 ? 'pnl-positive' : absolutePnL < 0 ? 'pnl-negative' : 'pnl-neutral';

        // Populate P&L data
        pnlContainer.innerHTML = `
            <div class="data-row">
                <span class="data-label">Entry Price:</span>
                <span class="data-value">$${entryPrice.toFixed(2)}</span>
            </div>
            <div class="data-row">
                <span class="data-label">Exit Price:</span>
                <span class="data-value">$${exitPrice.toFixed(2)}</span>
            </div>
            <div class="data-row">
                <span class="data-label">Price Difference:</span>
                <span class="data-value ${colorClass}">$${priceDiff.toFixed(2)}</span>
            </div>
            <div class="data-row">
                <span class="data-label">Quantity:</span>
                <span class="data-value">${quantity}</span>
            </div>
            <div class="data-row">
                <span class="data-label">Absolute P&L:</span>
                <span class="data-value ${colorClass}">$${absolutePnL.toFixed(2)}</span>
            </div>
            <div class="data-row">
                <span class="data-label">Percentage P&L:</span>
                <span class="data-value ${colorClass}">${percentagePnL.toFixed(2)}%</span>
            </div>
        `;

        pnlSection.style.display = 'block';

        // Update quantity input if empty
        if (!quantityInput.value) {
            quantityInput.value = entryQuantity;
        }

        // Add event listener to quantity input to update P&L in real-time
        quantityInput.removeEventListener('input', this.updatePnLCalculation.bind(this));
        quantityInput.addEventListener('input', this.updatePnLCalculation.bind(this));
    }

    addMarkToChart(markData) {
        // Store mark data
        this.marks.set(markData.id, markData);

        // Handle both old and new schema formats
        let side = markData.side || (markData.entry_side ? markData.entry_side.toLowerCase() : null) || 'buy';

        // Ensure side is a valid string
        if (!side || typeof side !== 'string') {
            side = 'buy'; // Default fallback
        }

        const entryPrice = markData.entry_price || markData.price || 0;
        const entryTimestamp = markData.entry_timestamp || markData.timestamp;

        // Add visual marker to chart
        const color = side === 'buy' ? '#4caf50' : '#f44336';

        const marker = {
            time: entryTimestamp,
            position: 'belowBar',
            color: color,
            shape: 'circle',
            text: `${side.toUpperCase()} @ $${entryPrice.toFixed(2)}`,
            size: 2
        };

        // Add marker to candlestick series
        if (this.chart.candlestickSeries) {
            const existingMarkers = this.chart.candlestickSeries.markers() || [];
            this.chart.candlestickSeries.setMarkers([...existingMarkers, marker]);
        }
    }

    updateMarkOnChart(markData) {
        // Update stored mark data
        this.marks.set(markData.id, markData);

        // Update visual representation if needed
        this.refreshChartMarkers();
    }

    refreshChartMarkers() {
        if (!this.chart.candlestickSeries) return;

        const markers = [];
        this.marks.forEach(mark => {
            // Handle both old and new schema formats
            let side = mark.side || (mark.entry_side ? mark.entry_side.toLowerCase() : null) || 'buy';

            // Ensure side is a valid string
            if (!side || typeof side !== 'string') {
                side = 'buy'; // Default fallback
            }

            const entryPrice = mark.entry_price || mark.price || 0;
            const entryTimestamp = mark.entry_timestamp || mark.timestamp;
            const exitPrice = mark.exit_price || null;
            const exitTimestamp = mark.exit_timestamp || null;

            const color = side === 'buy' ? '#4caf50' : '#f44336';

            // Entry marker
            markers.push({
                time: entryTimestamp,
                position: 'belowBar',
                color: color,
                shape: 'circle',
                text: `${side.toUpperCase()} @ $${entryPrice.toFixed(2)}`,
                size: 2
            });

            // Exit marker if exists
            if (exitTimestamp && exitPrice) {
                markers.push({
                    time: exitTimestamp,
                    position: 'aboveBar',
                    color: color,
                    shape: 'square',
                    text: `EXIT @ $${exitPrice.toFixed(2)}`,
                    size: 2
                });
            }
        });

        this.chart.candlestickSeries.setMarkers(markers);
    }

    async loadExistingMarks() {
        try {
            const response = await fetch('/api/v1/marks');
            const result = await response.json();
            
            if (result.success && result.data) {
                result.data.forEach(mark => {
                    this.marks.set(mark.id, mark);
                });
                
                this.refreshChartMarkers();
                this.updateSidebar();
            }
        } catch (error) {
            console.error('Error loading existing marks:', error);
        }
    }

    updateSidebar() {
        this.updateMarksList();
        this.updateStatistics();
    }

    updateMarksList() {
        const container = document.getElementById('active-marks-list');
        
        if (this.marks.size === 0) {
            container.innerHTML = '<div class="no-marks">No active marks</div>';
            return;
        }

        let html = '';
        this.marks.forEach(mark => {
            // Handle both old and new schema formats
            const side = mark.side || (mark.entry_side ? mark.entry_side.toLowerCase() : null) || 'buy';
            const entryPrice = mark.entry_price || mark.price || 0;
            const quantity = mark.quantity || 1; // Default quantity for new schema
            const exitPrice = mark.exit_price || null;

            const pnl = exitPrice ?
                ((exitPrice - entryPrice) * (side === 'buy' ? 1 : -1) * quantity).toFixed(2) :
                'Open';

            html += `
                <div class="mark-item">
                    <div class="mark-info">
                        <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 4px;">
                            <span class="mark-side ${side}">${side}</span>
                            <span>$${entryPrice.toFixed(2)}</span>
                            <span style="font-size: 11px; color: #888;">${quantity}</span>
                        </div>
                        <div style="font-size: 11px; color: ${pnl === 'Open' ? '#888' : (parseFloat(pnl) >= 0 ? '#4caf50' : '#f44336')};">
                            P&L: ${pnl === 'Open' ? pnl : '$' + pnl}
                        </div>
                    </div>
                </div>
            `;
        });

        container.innerHTML = html;
    }

    updateStatistics() {
        const totalEntries = this.marks.size;
        const openPositions = Array.from(this.marks.values()).filter(m => m.status === 'open').length;
        const closedTrades = totalEntries - openPositions;
        
        const closedMarks = Array.from(this.marks.values()).filter(m => m.status === 'closed');
        const winningTrades = closedMarks.filter(m => {
            const pnl = (m.exit_price - m.entry_price) * (m.side === 'buy' ? 1 : -1);
            return pnl > 0;
        }).length;
        
        const winRate = closedTrades > 0 ? ((winningTrades / closedTrades) * 100).toFixed(1) : 0;

        document.getElementById('total-entries').textContent = totalEntries;
        document.getElementById('open-positions').textContent = openPositions;
        document.getElementById('closed-trades').textContent = closedTrades;
        document.getElementById('win-rate').textContent = winRate + '%';
    }

    enableMarkingMode() {
        console.log('enableMarkingMode called');
        this.isMarkingMode = true;
        console.log('Marking mode enabled - isMarkingMode:', this.isMarkingMode);
        console.log('Chart container available:', !!this.chart?.container);

        // Update status
        const statusElement = document.getElementById('chart-status');
        if (statusElement) {
            statusElement.textContent = 'Marking mode active - Click to add entries, right-click for exits';
            statusElement.className = 'status warning';
        }
    }

    disableMarkingMode() {
        console.log('disableMarkingMode called');
        this.isMarkingMode = false;
        console.log('Marking mode disabled - isMarkingMode:', this.isMarkingMode);

        // Update status
        const statusElement = document.getElementById('chart-status');
        if (statusElement) {
            statusElement.textContent = 'Chart ready';
            statusElement.className = 'status info';
        }
    }

    async exportMarks() {
        try {
            const response = await fetch('/api/v1/marks/export');
            const blob = await response.blob();
            
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `marks_export_${new Date().toISOString().split('T')[0]}.csv`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(url);
        } catch (error) {
            console.error('Error exporting marks:', error);
            alert('Error exporting marks');
        }
    }

    async clearAllMarks() {
        if (!confirm('Are you sure you want to clear all marks? This action cannot be undone.')) {
            return;
        }

        try {
            const response = await fetch('/api/v1/marks', {
                method: 'DELETE'
            });

            const result = await response.json();
            
            if (result.success) {
                this.marks.clear();
                this.refreshChartMarkers();
                this.updateSidebar();
                console.log('All marks cleared');
            } else {
                alert('Error clearing marks: ' + result.message);
            }
        } catch (error) {
            console.error('Error clearing marks:', error);
            alert('Error clearing marks');
        }
    }
}

// Global marking tools instance
let markingTools = null;

// Test function for debugging
window.testMarkingTools = function() {
    console.log('=== Marking Tools Debug Info ===');
    console.log('markingTools instance:', markingTools);
    console.log('window.markingTools:', window.markingTools);
    console.log('window.tradingViewChart:', window.tradingViewChart);
    console.log('window.professionalChart:', window.professionalChart);
    console.log('MarkingTools class available:', typeof MarkingTools !== 'undefined');

    const activeChart = window.professionalChart || window.tradingViewChart;
    console.log('Active chart instance:', activeChart);

    if (markingTools) {
        console.log('Marking mode active:', markingTools.isMarkingMode);
        console.log('Chart available:', !!markingTools.chart);
        console.log('Chart container:', markingTools.chart?.container);

        // Debug chart data access
        console.log('=== Chart Data Debug ===');
        console.log('chart.currentData:', markingTools.chart?.currentData?.length || 'not available');
        console.log('chart.dataFeed:', !!markingTools.chart?.dataFeed);
        console.log('chart.dataFeed.data:', markingTools.chart?.dataFeed?.data?.length || 'not available');

        if (markingTools.chart?.dataFeed?.getData) {
            const data = markingTools.chart.dataFeed.getData();
            console.log('chart.dataFeed.getData():', data?.length || 'not available');
        }
    }

    // Test modal availability
    console.log('Entry modal:', document.getElementById('entry-modal'));
    console.log('Exit modal:', document.getElementById('exit-modal'));
    console.log('Marking sidebar:', document.getElementById('marking-sidebar'));

    return {
        markingTools,
        tradingViewChart: window.tradingViewChart,
        professionalChart: window.professionalChart,
        activeChart,
        modalsAvailable: {
            entry: !!document.getElementById('entry-modal'),
            exit: !!document.getElementById('exit-modal')
        }
    };
};

// Test function to simulate entry marking
window.testEntryModal = function() {
    console.log('Testing entry modal...');
    if (window.markingTools) {
        // Simulate click data
        window.markingTools.currentClickData = {
            time: Math.floor(Date.now() / 1000),
            price: 45000,
            x: 100,
            y: 100
        };
        window.markingTools.showEntryModal();
    } else {
        console.error('Marking tools not available');
    }
};

// Test function to simulate a complete entry/exit cycle
window.testMarkingCycle = function() {
    console.log('Testing complete marking cycle...');
    if (!window.markingTools) {
        console.error('Marking tools not available');
        return;
    }

    // Enable marking mode
    window.markingTools.enableMarkingMode();

    // Simulate entry
    const entryData = {
        id: Date.now(),
        entry_timestamp: Math.floor(Date.now() / 1000),
        entry_price: 45000,
        side: 'buy',
        quantity: 0.1,
        status: 'open',
        comprehensiveData: {
            symbol: 'BTCUSDT',
            timeframe: '15m',
            entry: {
                timestamp: new Date().toISOString(),
                entry_side: 'Buy',
                price: 45000,
                ohlcv: {
                    open: 44950,
                    high: 45100,
                    low: 44900,
                    close: 45000,
                    volume: 1500
                },
                indicators: {
                    ema: { ema_50: 44800, ema_100: 44600, ema_200: 44400 },
                    rsi: { rsi_6: 65, rsi_12: 62, rsi_24: 58 }
                }
            }
        }
    };

// Test function to compare entry and exit modal data display
window.testModalDataDisplay = async function() {
    console.log('🧪 Testing modal data display comparison...');

    if (!window.markingTools) {
        console.error('❌ Marking tools not available');
        return false;
    }

    try {
        // Simulate click data
        window.markingTools.currentClickData = {
            time: Math.floor(Date.now() / 1000),
            price: 45000,
            x: 100,
            y: 100
        };

        console.log('🔍 Testing entry modal data display...');
        await window.markingTools.showEntryModal();

        // Wait a moment for data to load
        await new Promise(resolve => setTimeout(resolve, 1000));

        // Check entry modal data
        const entryOHLCV = document.getElementById('entry-ohlcv');
        const entryIndicators = document.getElementById('entry-indicators');

        const entryHasOHLCV = entryOHLCV && entryOHLCV.innerHTML.trim() !== '' && !entryOHLCV.innerHTML.includes('No OHLCV data available');
        const entryHasIndicators = entryIndicators && entryIndicators.innerHTML.trim() !== '' && !entryIndicators.innerHTML.includes('No indicator data available');

        console.log('📊 Entry modal results:');
        console.log(`   OHLCV: ${entryHasOHLCV ? '✅ Has data' : '❌ No data'}`);
        console.log(`   Indicators: ${entryHasIndicators ? '✅ Has data' : '❌ No data'}`);
        if (entryOHLCV) console.log(`   OHLCV content preview: ${entryOHLCV.innerHTML.substring(0, 100)}...`);
        if (entryIndicators) console.log(`   Indicators content preview: ${entryIndicators.innerHTML.substring(0, 100)}...`);

        // Close entry modal
        const entryCloseBtn = document.getElementById('entry-modal-close');
        if (entryCloseBtn) entryCloseBtn.click();

        // Wait a moment
        await new Promise(resolve => setTimeout(resolve, 500));

        console.log('🔍 Testing exit modal data display...');
        await window.markingTools.showExitModal();

        // Wait a moment for data to load
        await new Promise(resolve => setTimeout(resolve, 1000));

        // Check exit modal data
        const exitOHLCV = document.getElementById('exit-ohlcv');
        const exitIndicators = document.getElementById('exit-indicators');

        const exitHasOHLCV = exitOHLCV && exitOHLCV.innerHTML.trim() !== '' && !exitOHLCV.innerHTML.includes('No OHLCV data available');
        const exitHasIndicators = exitIndicators && exitIndicators.innerHTML.trim() !== '' && !exitIndicators.innerHTML.includes('No indicator data available');

        console.log('📊 Exit modal results:');
        console.log(`   OHLCV: ${exitHasOHLCV ? '✅ Has data' : '❌ No data'}`);
        console.log(`   Indicators: ${exitHasIndicators ? '✅ Has data' : '❌ No data'}`);
        if (exitOHLCV) console.log(`   OHLCV content preview: ${exitOHLCV.innerHTML.substring(0, 100)}...`);
        if (exitIndicators) console.log(`   Indicators content preview: ${exitIndicators.innerHTML.substring(0, 100)}...`);

        // Close exit modal
        const exitCloseBtn = document.getElementById('exit-modal-close');
        if (exitCloseBtn) exitCloseBtn.click();

        // Compare results
        console.log('📈 Comparison results:');
        const ohlcvMatch = entryHasOHLCV === exitHasOHLCV;
        const indicatorsMatch = entryHasIndicators === exitHasIndicators;

        console.log(`   OHLCV consistency: ${ohlcvMatch ? '✅ Consistent' : '❌ Inconsistent'}`);
        console.log(`   Indicators consistency: ${indicatorsMatch ? '✅ Consistent' : '❌ Inconsistent'}`);

        const overallSuccess = ohlcvMatch && indicatorsMatch;
        console.log(`🎯 Overall test result: ${overallSuccess ? '✅ PASSED' : '❌ FAILED'}`);

        return overallSuccess;

    } catch (error) {
        console.error('❌ Modal data display test failed:', error);
        return false;
    }
};

    window.markingTools.addMarkToChart(entryData);
    window.markingTools.updateSidebar();

    console.log('✅ Test entry added successfully');
    console.log('You can now test the exit modal by right-clicking the chart');
};

// Test end-to-end exit mark creation
window.testExitMarkCreation = async function() {
    console.log('🧪 Testing End-to-End Exit Mark Creation');
    console.log('=' * 50);

    if (!window.markingTools) {
        console.error('❌ MarkingTools not initialized');
        return false;
    }

    try {
        // Step 1: Create a test entry mark
        console.log('📝 Step 1: Creating test entry mark...');

        const testEntryData = {
            id: Date.now(),
            symbol: 'BTCUSDT',
            timeframe: '15m',
            mark_type: 'ENTRY',
            entry_side: 'BUY',
            timestamp: Math.floor(Date.now() / 1000) - 3600, // 1 hour ago
            price: 45000.50,
            quantity: 0.1,
            notes: 'Test entry for exit testing',
            status: 'open',
            side: 'buy',
            ohlcv_snapshot: JSON.stringify({
                time: Math.floor(Date.now() / 1000) - 3600,
                open: 44950.0,
                high: 45100.0,
                low: 44900.0,
                close: 45000.0,
                volume: 1500.0
            }),
            indicator_snapshot: JSON.stringify({
                ema: { ema_20: 44800.0, ema_50: 44600.0 },
                rsi: { rsi_14: 65.5 }
            })
        };

        window.markingTools.addMarkToChart(testEntryData);
        console.log('✅ Test entry mark created with ID:', testEntryData.id);

        // Step 2: Test entry selection population
        console.log('📝 Step 2: Testing entry selection population...');

        // Simulate opening exit modal
        window.markingTools.currentClickData = {
            time: Math.floor(Date.now() / 1000), // Current time
            price: 46500.75
        };

        // Create a temporary select element to test population
        const testSelect = document.createElement('select');
        testSelect.id = 'test-exit-entry-select';
        testSelect.innerHTML = '<option value="">Select an open entry...</option>';
        document.body.appendChild(testSelect);

        // Test the populateOpenEntries method
        const originalSelect = document.getElementById('exit-entry-select');
        const tempOriginal = originalSelect;

        // Temporarily replace the select element
        if (originalSelect) {
            originalSelect.id = 'temp-exit-entry-select';
        }
        testSelect.id = 'exit-entry-select';

        window.markingTools.populateOpenEntries();

        // Check if entry was populated
        const options = testSelect.querySelectorAll('option');
        const hasEntryOption = options.length > 1; // More than just the default option

        console.log(`📊 Entry options found: ${options.length - 1}`);
        if (hasEntryOption) {
            console.log('✅ Entry selection populated successfully');
            for (let i = 1; i < options.length; i++) {
                console.log(`   - ${options[i].textContent} (value: ${options[i].value})`);
            }
        } else {
            console.log('❌ No entry options found');
        }

        // Restore original select
        testSelect.remove();
        if (tempOriginal) {
            tempOriginal.id = 'exit-entry-select';
        }

        // Step 3: Test data extraction
        console.log('📝 Step 3: Testing data extraction...');

        const ohlcvData = await window.markingTools.getOHLCVData(window.markingTools.currentClickData.time);
        const indicatorData = await window.markingTools.getIndicatorData(window.markingTools.currentClickData.time);

        console.log('📊 OHLCV Data:', ohlcvData ? 'Available' : 'Not available');
        console.log('📊 Indicator Data:', indicatorData ? 'Available' : 'Not available');

        if (ohlcvData) {
            console.log('   OHLCV:', ohlcvData);
        }
        if (indicatorData) {
            console.log('   Indicators:', indicatorData);
        }

        // Step 4: Test exit mark creation (simulation)
        console.log('📝 Step 4: Testing exit mark creation logic...');

        const exitTestData = {
            entry_id: testEntryData.id,
            timestamp: window.markingTools.currentClickData.time,
            price: window.markingTools.currentClickData.price,
            quantity: 0.1,
            notes: 'Test exit mark',
            ohlcv_data: ohlcvData,
            indicator_data: indicatorData
        };

        console.log('📤 Exit data structure:', exitTestData);

        // Test comprehensive data creation
        const selectedEntry = window.markingTools.marks.get(testEntryData.id);
        if (selectedEntry) {
            console.log('✅ Selected entry found for exit creation');

            const entryPrice = selectedEntry.entry_price || selectedEntry.price || 0;
            const side = selectedEntry.side || 'buy';
            const priceDiff = exitTestData.price - entryPrice;
            const profitPct = (priceDiff / entryPrice) * 100;

            console.log(`📊 P&L Calculation: ${priceDiff.toFixed(2)} (${profitPct.toFixed(2)}%)`);
            console.log('✅ Exit mark creation logic working');
        } else {
            console.log('❌ Selected entry not found for exit creation');
        }

        // Summary
        console.log('\n📊 Test Summary:');
        console.log(`   ✅ Entry Mark Creation: PASS`);
        console.log(`   ${hasEntryOption ? '✅' : '❌'} Entry Selection: ${hasEntryOption ? 'PASS' : 'FAIL'}`);
        console.log(`   ${ohlcvData ? '✅' : '⚠️'} OHLCV Data Extraction: ${ohlcvData ? 'PASS' : 'NO DATA'}`);
        console.log(`   ${indicatorData ? '✅' : '⚠️'} Indicator Data Extraction: ${indicatorData ? 'PASS' : 'NO DATA'}`);
        console.log(`   ✅ Exit Logic: PASS`);

        const allPassed = hasEntryOption && (ohlcvData || indicatorData);
        console.log(`\n🎯 Overall Result: ${allPassed ? '✅ PASS' : '⚠️ PARTIAL'}`);

        if (!allPassed) {
            console.log('\n💡 Notes:');
            if (!hasEntryOption) {
                console.log('   - Entry selection failed - check mark storage and retrieval');
            }
            if (!ohlcvData && !indicatorData) {
                console.log('   - No market data available - this is expected if no real chart data is loaded');
            }
        }

        return allPassed;

    } catch (error) {
        console.error('❌ Test failed with error:', error);
        return false;
    }
};

// Debug chart data access
window.debugChartData = function() {
    console.log('=== Chart Data Access Debug ===');

    const charts = [
        { name: 'window.tradingViewChart', instance: window.tradingViewChart },
        { name: 'window.professionalChart', instance: window.professionalChart }
    ];

    charts.forEach(({ name, instance }) => {
        if (instance) {
            console.log(`\n${name}:`);
            console.log('  - Instance exists:', !!instance);
            console.log('  - currentData:', instance.currentData?.length || 'not available');
            console.log('  - dataFeed:', !!instance.dataFeed);

            if (instance.dataFeed) {
                console.log('  - dataFeed.data:', instance.dataFeed.data?.length || 'not available');
                console.log('  - dataFeed.getData:', typeof instance.dataFeed.getData);

                if (typeof instance.dataFeed.getData === 'function') {
                    try {
                        const data = instance.dataFeed.getData();
                        console.log('  - dataFeed.getData() result:', data?.length || 'not available');
                        if (data && data.length > 0) {
                            console.log('  - Sample candle:', data[data.length - 1]);
                        }
                    } catch (error) {
                        console.log('  - dataFeed.getData() error:', error.message);
                    }
                }
            }

            // Check for other possible data properties
            const possibleDataProps = ['data', 'candleData', 'ohlcData', 'chartData', 'seriesData'];
            possibleDataProps.forEach(prop => {
                if (instance[prop]) {
                    console.log(`  - ${prop}:`, instance[prop]?.length || typeof instance[prop]);
                }
            });

            // Check chart series
            if (instance.candlestickSeries) {
                console.log('  - candlestickSeries exists:', !!instance.candlestickSeries);
                // Try to get data from series (this might not work but worth trying)
                try {
                    const seriesData = instance.candlestickSeries.data();
                    console.log('  - candlestickSeries.data():', seriesData?.length || 'not available');
                } catch (error) {
                    console.log('  - candlestickSeries.data() not available');
                }
            }
        }
    });

    return { tradingViewChart: window.tradingViewChart, professionalChart: window.professionalChart };
};

// Simple test function that works without chart data
window.testSimpleEntry = function() {
    console.log('Testing simple entry without chart data...');

    if (!window.markingTools) {
        console.error('Marking tools not available');
        return;
    }

    // Enable marking mode
    window.markingTools.enableMarkingMode();

    // Create simple click data with current timestamp
    const currentTime = Math.floor(Date.now() / 1000);
    const currentPrice = 45000 + (Math.random() - 0.5) * 1000; // Random price around 45k

    window.markingTools.currentClickData = {
        time: currentTime,
        price: currentPrice,
        x: 100,
        y: 100,
        candlestickData: null // No candlestick data
    };

    console.log('Created click data:', window.markingTools.currentClickData);

    // Show entry modal
    window.markingTools.showEntryModal();

    console.log('✅ Entry modal should be displayed');
    console.log('You can now fill in the form and test the entry creation');
};

// Comprehensive chart exploration function
window.exploreChartStructure = function() {
    console.log('=== Comprehensive Chart Structure Exploration ===');

    const chart = window.professionalChart || window.tradingViewChart;
    if (!chart) {
        console.log('No chart instance found');
        return;
    }

    console.log('Chart instance:', chart);
    console.log('Chart constructor:', chart.constructor.name);

    // Explore main chart object
    console.log('\n=== Main Chart Properties ===');
    const mainProps = Object.keys(chart);
    mainProps.forEach(prop => {
        const value = chart[prop];
        const type = typeof value;
        const isArray = Array.isArray(value);
        const length = isArray ? value.length : (value && typeof value.length === 'number' ? value.length : 'N/A');

        console.log(`${prop}: ${type}${isArray ? ' (array)' : ''} - length: ${length}`);

        if (prop === 'currentData' && isArray) {
            console.log(`  Sample currentData:`, value.slice(0, 2));
        }
    });

    // Explore candlestick series
    if (chart.candlestickSeries) {
        console.log('\n=== Candlestick Series Properties ===');
        console.log('Series object:', chart.candlestickSeries);
        console.log('Series constructor:', chart.candlestickSeries.constructor.name);

        // Get all properties including non-enumerable ones
        const seriesProps = [];
        let obj = chart.candlestickSeries;
        while (obj && obj !== Object.prototype) {
            seriesProps.push(...Object.getOwnPropertyNames(obj));
            obj = Object.getPrototypeOf(obj);
        }

        const uniqueProps = [...new Set(seriesProps)];
        console.log('All series properties:', uniqueProps);

        // Check specific properties that might contain data
        const dataProps = ['data', '_data', 'seriesData', '_seriesData', 'model', '_model', 'dataProvider', '_dataProvider'];
        dataProps.forEach(prop => {
            if (chart.candlestickSeries[prop]) {
                console.log(`${prop}:`, chart.candlestickSeries[prop]);
            }
        });
    }

    // Explore dataFeed
    if (chart.dataFeed) {
        console.log('\n=== DataFeed Properties ===');
        console.log('DataFeed object:', chart.dataFeed);
        console.log('DataFeed data length:', chart.dataFeed.data?.length);
        console.log('DataFeed sample data:', chart.dataFeed.data?.slice(-2));
    }

    // Try to get data using our method
    if (window.markingTools) {
        console.log('\n=== Testing Data Access Methods ===');
        const testData = window.markingTools.tryGetDataFromSeries();
        console.log('tryGetDataFromSeries result:', testData);
    }

    return chart;
};

// Targeted exploration of TradingView internal data structures
window.exploreTradingViewInternals = function() {
    console.log('=== TradingView Internal Data Exploration ===');

    const chart = window.professionalChart || window.tradingViewChart;
    if (!chart || !chart.candlestickSeries) {
        console.log('No chart or candlestick series found');
        return;
    }

    const series = chart.candlestickSeries;
    console.log('Candlestick series:', series);

    // Focus on the most promising internal properties
    const internalProps = [
        '_internal', '_dataSource', '_series', '_seriesModel', '_model',
        'source', '_source', 'bars', '_bars', 'points', '_points'
    ];

    internalProps.forEach(prop => {
        if (series[prop]) {
            console.log(`\n=== ${prop} ===`);
            console.log('Type:', typeof series[prop]);
            console.log('Value:', series[prop]);

            if (typeof series[prop] === 'object' && series[prop] !== null) {
                console.log('Keys:', Object.keys(series[prop]));

                // Look for data-like properties
                const dataProps = ['data', '_data', 'items', '_items', 'values', '_values', 'bars', '_bars'];
                dataProps.forEach(dataProp => {
                    if (series[prop][dataProp]) {
                        console.log(`  ${dataProp}:`, typeof series[prop][dataProp]);
                        if (Array.isArray(series[prop][dataProp])) {
                            console.log(`    Array length: ${series[prop][dataProp].length}`);
                            if (series[prop][dataProp].length > 0) {
                                console.log(`    Sample items:`, series[prop][dataProp].slice(0, 3));
                            }
                        }
                    }
                });
            }
        }
    });

    // Also check if there are any methods that might return data
    console.log('\n=== Available Methods ===');
    const methods = [];
    let obj = series;
    while (obj && obj !== Object.prototype) {
        Object.getOwnPropertyNames(obj).forEach(name => {
            if (typeof series[name] === 'function' && !methods.includes(name)) {
                methods.push(name);
            }
        });
        obj = Object.getPrototypeOf(obj);
    }

    console.log('All methods:', methods);

    // Try some promising methods
    const dataMethods = ['data', 'getData', 'dataByIndex', 'priceToCoordinate', 'coordinateToPrice'];
    dataMethods.forEach(method => {
        if (typeof series[method] === 'function') {
            try {
                console.log(`Trying ${method}()...`);
                const result = series[method]();
                console.log(`${method}() result:`, result);
            } catch (error) {
                console.log(`${method}() error:`, error.message);
            }
        }
    });

    return series;
};

// Comprehensive test of all data extraction methods
window.testAllDataExtractionMethods = function() {
    console.log('=== Testing All Data Extraction Methods ===');

    const chart = window.professionalChart || window.tradingViewChart;
    if (!chart) {
        console.log('No chart instance found');
        return;
    }

    if (!window.markingTools) {
        console.log('No marking tools instance found');
        return;
    }

    // Test 1: Try the deep search method
    console.log('\n1. Testing deep search method...');
    const deepSearchResult = window.markingTools.tryGetDataFromSeries();
    console.log('Deep search result:', deepSearchResult);

    // Test 2: Check dataFeed data
    console.log('\n2. Testing dataFeed access...');
    if (chart.dataFeed) {
        console.log('DataFeed exists:', !!chart.dataFeed);
        console.log('DataFeed data:', chart.dataFeed.data?.length || 'no data');
        if (chart.dataFeed.data && chart.dataFeed.data.length > 0) {
            console.log('Sample dataFeed data:', chart.dataFeed.data.slice(-3));
        }
    }

    // Test 3: Check currentData
    console.log('\n3. Testing currentData access...');
    console.log('CurrentData exists:', !!chart.currentData);
    console.log('CurrentData length:', chart.currentData?.length || 0);
    if (chart.currentData && chart.currentData.length > 0) {
        console.log('Sample currentData:', chart.currentData.slice(-3));
    }

    // Test 4: Try to get data through getCandlestickAtTime
    console.log('\n4. Testing getCandlestickAtTime...');
    const currentTime = Math.floor(Date.now() / 1000);
    const candlestickData = window.markingTools.getCandlestickAtTime(currentTime);
    console.log('getCandlestickAtTime result:', candlestickData);

    // Test 5: Manual exploration of series internals
    console.log('\n5. Manual series exploration...');
    if (chart.candlestickSeries) {
        const series = chart.candlestickSeries;

        // Check for common TradingView internal properties
        const internalPaths = [
            '_internal',
            '_internal._model',
            '_internal._model._data',
            '_internal._dataSource',
            '_internal._series'
        ];

        internalPaths.forEach(path => {
            try {
                const value = path.split('.').reduce((obj, prop) => obj?.[prop], series);
                if (value) {
                    console.log(`Found ${path}:`, typeof value);
                    if (Array.isArray(value)) {
                        console.log(`  Array length: ${value.length}`);
                        if (value.length > 0) {
                            console.log(`  Sample:`, value.slice(0, 2));
                        }
                    } else if (typeof value === 'object') {
                        console.log(`  Object keys:`, Object.keys(value));
                    }
                }
            } catch (error) {
                // Path doesn't exist, continue
            }
        });
    }

    // Summary
    console.log('\n=== Summary ===');
    const hasDataFeedData = chart.dataFeed?.data?.length > 0;
    const hasCurrentData = chart.currentData?.length > 0;
    const hasDeepSearchData = !!deepSearchResult;

    console.log('Data sources found:');
    console.log('  - DataFeed data:', hasDataFeedData ? '✅' : '❌');
    console.log('  - CurrentData:', hasCurrentData ? '✅' : '❌');
    console.log('  - Deep search:', hasDeepSearchData ? '✅' : '❌');

    if (hasDataFeedData) {
        console.log('\n🎉 SUCCESS: DataFeed data is available!');
        console.log('Use chart.dataFeed.data to access candlestick data');
        return chart.dataFeed.data;
    } else if (hasCurrentData) {
        console.log('\n🎉 SUCCESS: CurrentData is available!');
        console.log('Use chart.currentData to access candlestick data');
        return chart.currentData;
    } else if (hasDeepSearchData) {
        console.log('\n🎉 SUCCESS: Deep search found data!');
        return deepSearchResult;
    } else {
        console.log('\n❌ No candlestick data found through any method');
        return null;
    }
};

// Initialize when chart is ready
document.addEventListener('DOMContentLoaded', () => {
    // Wait for chart to be initialized
    const initMarkingTools = () => {
        console.log('Checking chart readiness...');
        console.log('window.tradingViewChart:', !!window.tradingViewChart);
        console.log('window.professionalChart:', !!window.professionalChart);

        // Try both chart instances - prefer professionalChart for strategy builder
        const chartInstance = window.professionalChart || window.tradingViewChart;

        if (chartInstance && chartInstance.container) {
            markingTools = new MarkingTools(chartInstance);
            window.markingTools = markingTools;
            console.log('✅ Marking tools initialized successfully!');
            console.log('Using chart:', chartInstance === window.professionalChart ? 'professionalChart' : 'tradingViewChart');
            console.log('Chart container:', chartInstance.container);
        } else {
            console.log('⏳ Waiting for chart to be ready...');
            console.log('Available charts:', {
                tradingViewChart: !!window.tradingViewChart,
                professionalChart: !!window.professionalChart,
                containerReady: !!chartInstance?.container
            });
            setTimeout(initMarkingTools, 500);
        }
    };

    setTimeout(initMarkingTools, 1000);
});

// Quick test function for modal data display
window.testExitModalData = async function() {
    console.log('🧪 Quick test: Exit modal data display');

    if (!window.markingTools) {
        console.error('❌ Marking tools not available');
        return;
    }

    // Simulate click data
    window.markingTools.currentClickData = {
        time: Math.floor(Date.now() / 1000),
        price: 45000,
        x: 100,
        y: 100
    };

    console.log('🔍 Opening exit modal...');
    await window.markingTools.showExitModal();

    // Wait for data to load
    setTimeout(() => {
        const ohlcvContainer = document.getElementById('exit-ohlcv');
        const indicatorContainer = document.getElementById('exit-indicators');

        console.log('📊 Exit modal data check:');
        console.log('   OHLCV container found:', !!ohlcvContainer);
        console.log('   OHLCV content:', ohlcvContainer ? ohlcvContainer.innerHTML.substring(0, 200) + '...' : 'N/A');
        console.log('   Indicator container found:', !!indicatorContainer);
        console.log('   Indicator content:', indicatorContainer ? indicatorContainer.innerHTML.substring(0, 200) + '...' : 'N/A');

        const hasOHLCV = ohlcvContainer && ohlcvContainer.innerHTML.trim() !== '' && !ohlcvContainer.innerHTML.includes('No OHLCV data available');
        const hasIndicators = indicatorContainer && indicatorContainer.innerHTML.trim() !== '' && !indicatorContainer.innerHTML.includes('No indicator data available');

        console.log('📈 Results:');
        console.log(`   OHLCV data: ${hasOHLCV ? '✅ Present' : '❌ Missing'}`);
        console.log(`   Indicator data: ${hasIndicators ? '✅ Present' : '❌ Missing'}`);

        if (!hasOHLCV) {
            console.log('🔍 Debugging OHLCV extraction...');
            window.markingTools.getOHLCVData(window.markingTools.currentClickData.time).then(data => {
                console.log('   Direct OHLCV call result:', data);
            });
        }

        if (!hasIndicators) {
            console.log('🔍 Debugging indicator extraction...');
            window.markingTools.getIndicatorData(window.markingTools.currentClickData.time).then(data => {
                console.log('   Direct indicator call result:', data);
            });
        }
    }, 1500);
};

// Test function to verify both modals work independently after restoration
window.testRestoredModals = async function() {
    console.log('🧪 Testing restored entry modal vs enhanced exit modal...');

    if (!window.markingTools) {
        console.error('❌ Marking tools not available');
        return;
    }

    // Set up test click data
    window.markingTools.currentClickData = {
        time: Math.floor(Date.now() / 1000),
        price: 45000,
        x: 100,
        y: 100
    };

    console.log('🔍 Testing RESTORED entry modal (clean version)...');
    await window.markingTools.showEntryModal();

    // Wait a moment
    await new Promise(resolve => setTimeout(resolve, 2000));

    // Close entry modal
    const entryModal = document.getElementById('entry-modal');
    if (entryModal) entryModal.style.display = 'none';

    console.log('🔍 Testing ENHANCED exit modal (with debugging)...');
    await window.markingTools.showExitModal();

    console.log('✅ Both modals tested successfully!');
    console.log('📋 Entry modal: Restored to clean working state');
    console.log('📋 Exit modal: Maintains enhanced debugging features');
};

// Test function to debug indicator data availability
window.debugIndicators = function() {
    console.log('🔍 Debugging indicator system...');

    console.log('📊 Indicators Manager:', {
        exists: !!window.indicatorsManager,
        hasIndicatorSeries: !!window.indicatorsManager?.indicatorSeries,
        seriesKeys: window.indicatorsManager?.indicatorSeries ? Object.keys(window.indicatorsManager.indicatorSeries) : [],
        seriesCount: window.indicatorsManager?.indicatorSeries ? Object.keys(window.indicatorsManager.indicatorSeries).length : 0
    });

    if (window.indicatorsManager?.indicatorSeries) {
        const series = window.indicatorsManager.indicatorSeries;
        Object.keys(series).forEach(key => {
            const seriesData = series[key];
            console.log(`📈 Series "${key}":`, {
                hasData: !!seriesData?.data,
                dataLength: seriesData?.data?.length || 0,
                latestValue: seriesData?.data?.[seriesData.data.length - 1]
            });
        });
    }

    console.log('🌐 Chart objects:', {
        chart: !!window.chart,
        tradingViewChart: !!window.tradingViewChart,
        multiPanelChart: !!window.multiPanelChart
    });
};

// Test function specifically for indicator data extraction
window.testIndicatorExtraction = async function() {
    console.log('🧪 Testing indicator data extraction...');

    if (!window.markingTools) {
        console.error('❌ Marking tools not available');
        return;
    }

    // Set up test click data
    window.markingTools.currentClickData = {
        time: Math.floor(Date.now() / 1000),
        price: 45000,
        x: 100,
        y: 100
    };

    console.log('🔍 Testing getIndicatorData method...');
    const indicatorData = await window.markingTools.getIndicatorData(window.markingTools.currentClickData.time);

    console.log('📊 Indicator extraction result:', indicatorData);

    if (indicatorData) {
        console.log('✅ Indicators found:', Object.keys(indicatorData));
        Object.keys(indicatorData).forEach(type => {
            console.log(`   ${type}:`, indicatorData[type]);
        });
    } else {
        console.log('❌ No indicator data found');
        console.log('💡 Make sure indicators are plotted on the chart first');
    }
};

// Test function to debug OHLCV data extraction for both modals
window.testOHLCVExtraction = async function() {
    console.log('🧪 Testing OHLCV data extraction for both modals...');

    if (!window.markingTools) {
        console.error('❌ Marking tools not available');
        return;
    }

    // Set up test click data with candlestick data
    window.markingTools.currentClickData = {
        time: Math.floor(Date.now() / 1000),
        price: 45000,
        x: 100,
        y: 100,
        // Add sample candlestick data
        candlestickData: {
            time: Math.floor(Date.now() / 1000),
            open: 44950,
            high: 45100,
            low: 44900,
            close: 45000,
            volume: 1250000
        }
    };

    console.log('🔍 Testing OHLCV extraction with sample data...');
    const ohlcvData = await window.markingTools.getOHLCVData(window.markingTools.currentClickData.time);

    console.log('📊 OHLCV extraction result:', ohlcvData);

    if (ohlcvData) {
        console.log('✅ OHLCV data found:', ohlcvData);

        // Test entry modal
        console.log('🔍 Testing entry modal OHLCV display...');
        await window.markingTools.showEntryModal();

        // Wait a moment
        await new Promise(resolve => setTimeout(resolve, 1000));

        // Close entry modal
        const entryModal = document.getElementById('entry-modal');
        if (entryModal) entryModal.style.display = 'none';

        // Test exit modal
        console.log('🔍 Testing exit modal OHLCV display...');
        await window.markingTools.showExitModal();

        console.log('✅ Both modals tested with OHLCV data!');
    } else {
        console.log('❌ No OHLCV data found - check data source');
    }
};

// Comprehensive test function for both fixes
window.testBothFixes = async function() {
    console.log('🧪 Testing both MACD indicator display and OHLCV data fixes...');

    if (!window.markingTools) {
        console.error('❌ Marking tools not available');
        return;
    }

    // Set up test click data with both OHLCV and indicator data
    window.markingTools.currentClickData = {
        time: Math.floor(Date.now() / 1000),
        price: 45000,
        x: 100,
        y: 100,
        candlestickData: {
            time: Math.floor(Date.now() / 1000),
            open: 44950,
            high: 45100,
            low: 44900,
            close: 45000,
            volume: 1250000
        }
    };

    // Test entry modal first
    console.log('🔍 Testing ENTRY modal with fixes...');
    await window.markingTools.showEntryModal();

    // Wait for data to load
    await new Promise(resolve => setTimeout(resolve, 1500));

    // Check entry modal data
    const entryOHLCV = document.getElementById('entry-ohlcv');
    const entryIndicators = document.getElementById('entry-indicators');

    console.log('📊 Entry Modal Results:');
    console.log('   OHLCV section:', entryOHLCV ? 'Found' : 'Missing');
    console.log('   OHLCV content:', entryOHLCV?.innerHTML.includes('No OHLCV data') ? 'Empty' : 'Has data');
    console.log('   Indicators section:', entryIndicators ? 'Found' : 'Missing');
    console.log('   MACD display:', entryIndicators?.innerHTML.includes('MACD:') ? 'Has MACD line' : 'No MACD line');
    console.log('   Signal display:', entryIndicators?.innerHTML.includes('Signal:') ? 'Has Signal' : 'No Signal');
    console.log('   Histogram display:', entryIndicators?.innerHTML.includes('Histogram:') ? 'Has Histogram' : 'No Histogram');

    // Close entry modal
    const entryModal = document.getElementById('entry-modal');
    if (entryModal) entryModal.style.display = 'none';

    // Test exit modal
    console.log('🔍 Testing EXIT modal with fixes...');
    await window.markingTools.showExitModal();

    // Wait for data to load
    await new Promise(resolve => setTimeout(resolve, 1500));

    // Check exit modal data
    const exitOHLCV = document.getElementById('exit-ohlcv');
    const exitIndicators = document.getElementById('exit-indicators');

    console.log('📊 Exit Modal Results:');
    console.log('   OHLCV section:', exitOHLCV ? 'Found' : 'Missing');
    console.log('   OHLCV content:', exitOHLCV?.innerHTML.includes('No OHLCV data') ? 'Empty' : 'Has data');
    console.log('   Indicators section:', exitIndicators ? 'Found' : 'Missing');
    console.log('   MACD display:', exitIndicators?.innerHTML.includes('MACD:') ? 'Has MACD line' : 'No MACD line');
    console.log('   Signal display:', exitIndicators?.innerHTML.includes('Signal:') ? 'Has Signal' : 'No Signal');
    console.log('   Histogram display:', exitIndicators?.innerHTML.includes('Histogram:') ? 'Has Histogram' : 'No Histogram');

    console.log('✅ Both fixes tested! Check the results above.');
};

// Test function specifically for exit modal data extraction fix
window.testExitModalDataFix = async function() {
    console.log('🧪 Testing Exit Modal Data Extraction Fix...');

    if (!window.markingTools) {
        console.error('❌ Marking tools not available');
        return;
    }

    // Enable marking mode
    window.markingTools.enableMarkingMode();

    // Create realistic test click data with candlestick data
    const currentTime = Math.floor(Date.now() / 1000);
    const testPrice = 45123.45;

    window.markingTools.currentClickData = {
        time: currentTime,
        price: testPrice,
        x: 150,
        y: 200,
        candlestickData: {
            time: currentTime,
            open: 45000.12,
            high: 45200.89,
            low: 44950.33,
            close: testPrice,
            volume: 1567890
        }
    };

    console.log('📊 Test click data created:', window.markingTools.currentClickData);

    // Test exit modal
    console.log('🔍 Testing EXIT modal with real data extraction...');
    await window.markingTools.showExitModal();

    // Wait for data to load
    await new Promise(resolve => setTimeout(resolve, 2000));

    // Check if exit modal is displaying real data
    const exitSymbol = document.getElementById('exit-symbol')?.textContent;
    const exitTimeframe = document.getElementById('exit-timeframe')?.textContent;
    const exitTime = document.getElementById('exit-time')?.textContent;
    const exitPrice = document.getElementById('exit-price')?.textContent;

    console.log('📊 Exit Modal Data Display:');
    console.log('   Symbol:', exitSymbol);
    console.log('   Timeframe:', exitTimeframe);
    console.log('   Time:', exitTime);
    console.log('   Price:', exitPrice);
    console.log('   Expected Price: $' + testPrice.toFixed(2));

    // Check OHLCV data
    const exitOHLCV = document.getElementById('exit-ohlcv');
    const hasOHLCVData = exitOHLCV && !exitOHLCV.innerHTML.includes('No OHLCV data');

    console.log('📈 Exit Modal OHLCV:');
    console.log('   Has OHLCV data:', hasOHLCVData);
    if (hasOHLCVData) {
        console.log('   OHLCV content preview:', exitOHLCV.innerHTML.substring(0, 200) + '...');
    }

    // Check if price matches expected value
    const priceMatches = exitPrice && exitPrice.includes(testPrice.toFixed(2));
    console.log('✅ Price accuracy check:', priceMatches ? 'PASSED' : 'FAILED');

    if (!priceMatches) {
        console.error('❌ Exit modal is still showing incorrect price!');
        console.error('   Expected: $' + testPrice.toFixed(2));
        console.error('   Actual: ' + exitPrice);
    } else {
        console.log('✅ Exit modal is now showing correct real price!');
    }

    // Close modal
    const exitModal = document.getElementById('exit-modal');
    if (exitModal) exitModal.style.display = 'none';

    console.log('🎯 Exit modal data extraction test completed!');
    return {
        priceCorrect: priceMatches,
        hasOHLCV: hasOHLCVData,
        displayedPrice: exitPrice,
        expectedPrice: '$' + testPrice.toFixed(2)
    };
};

// Test function for unified modal functionality
window.testUnifiedModal = async function() {
    console.log('🧪 Testing Unified Modal Functionality...');

    if (!window.markingTools) {
        console.error('❌ Marking tools not available');
        return;
    }

    // Enable marking mode
    window.markingTools.enableMarkingMode();

    // Create test click data
    const currentTime = Math.floor(Date.now() / 1000);
    const testPrice = 45678.90;

    window.markingTools.currentClickData = {
        time: currentTime,
        price: testPrice,
        x: 200,
        y: 150,
        candlestickData: {
            time: currentTime,
            open: 45600.00,
            high: 45750.00,
            low: 45550.00,
            close: testPrice,
            volume: 2345678
        }
    };

    console.log('📊 Test click data created:', window.markingTools.currentClickData);

    // Test ENTRY modal
    console.log('🔍 Testing ENTRY modal...');
    await window.markingTools.showTradeModal('entry');

    // Wait for modal to load
    await new Promise(resolve => setTimeout(resolve, 1500));

    // Check entry modal configuration
    const modal = document.getElementById('trade-modal');
    const title = document.getElementById('trade-modal-title');
    const confirmButton = document.getElementById('trade-confirm');
    const entryForm = document.getElementById('trade-entry-form');
    const exitForm = document.getElementById('trade-exit-form');

    console.log('📊 Entry Modal Results:');
    console.log('   Modal visible:', modal?.style.display !== 'none');
    console.log('   Title:', title?.textContent);
    console.log('   Confirm button:', confirmButton?.textContent);
    console.log('   Entry form visible:', entryForm?.style.display !== 'none');
    console.log('   Exit form hidden:', exitForm?.style.display === 'none');
    console.log('   Current modal type:', window.markingTools.currentModalType);

    // Close modal
    window.markingTools.closeModal('trade-modal');

    // Wait a moment
    await new Promise(resolve => setTimeout(resolve, 500));

    // Test EXIT modal
    console.log('🔍 Testing EXIT modal...');
    await window.markingTools.showTradeModal('exit');

    // Wait for modal to load
    await new Promise(resolve => setTimeout(resolve, 1500));

    // Check exit modal configuration
    console.log('📊 Exit Modal Results:');
    console.log('   Modal visible:', modal?.style.display !== 'none');
    console.log('   Title:', title?.textContent);
    console.log('   Confirm button:', confirmButton?.textContent);
    console.log('   Entry form visible:', entryForm?.style.display !== 'none');
    console.log('   Exit form visible:', exitForm?.style.display !== 'none');
    console.log('   Current modal type:', window.markingTools.currentModalType);

    console.log('✅ Unified modal test completed!');

    return {
        entryModalWorking: title?.textContent === 'Add Entry Mark',
        exitModalWorking: title?.textContent === 'Add Exit Mark',
        modalTypeTracking: window.markingTools.currentModalType === 'exit'
    };
};

// Test function specifically for exit modal data persistence fix
window.testExitModalDataPersistence = async function() {
    console.log('🧪 Testing Exit Modal Data Persistence Fix...');

    if (!window.markingTools) {
        console.error('❌ Marking tools not available');
        return;
    }

    // Enable marking mode
    window.markingTools.enableMarkingMode();

    // Create realistic test click data with candlestick data
    const currentTime = Math.floor(Date.now() / 1000);
    const testPrice = 47890.12;

    window.markingTools.currentClickData = {
        time: currentTime,
        price: testPrice,
        x: 250,
        y: 180,
        candlestickData: {
            time: currentTime,
            open: 47800.50,
            high: 47950.75,
            low: 47750.25,
            close: testPrice,
            volume: 3456789
        }
    };

    console.log('📊 Test click data created:', window.markingTools.currentClickData);

    // Test exit modal data storage
    console.log('🔍 Testing EXIT modal data storage...');
    await window.markingTools.showTradeModal('exit');

    // Wait for modal to load and data to be processed
    await new Promise(resolve => setTimeout(resolve, 2000));

    // Check if trade data was stored
    const storedTradeData = window.markingTools.currentTradeData;
    console.log('💾 Stored trade data:', storedTradeData);

    // Verify stored data
    const dataStoredCorrectly = storedTradeData &&
                               storedTradeData.timestamp === currentTime &&
                               storedTradeData.price === testPrice &&
                               storedTradeData.symbol &&
                               storedTradeData.timeframe;

    console.log('📊 Data Storage Verification:');
    console.log('   Trade data stored:', !!storedTradeData);
    console.log('   Timestamp correct:', storedTradeData?.timestamp === currentTime);
    console.log('   Price correct:', storedTradeData?.price === testPrice);
    console.log('   Symbol present:', !!storedTradeData?.symbol);
    console.log('   Timeframe present:', !!storedTradeData?.timeframe);
    console.log('   Candlestick data present:', !!storedTradeData?.candlestickData);

    // Check modal display
    const modal = document.getElementById('trade-modal');
    const priceElement = document.getElementById('trade-price');
    const timeElement = document.getElementById('trade-time');

    const displayedPrice = priceElement?.textContent;
    const displayedTime = timeElement?.textContent;

    console.log('📊 Modal Display Verification:');
    console.log('   Modal visible:', modal?.style.display !== 'none');
    console.log('   Displayed price:', displayedPrice);
    console.log('   Expected price: $' + testPrice.toFixed(2));
    console.log('   Price matches:', displayedPrice?.includes(testPrice.toFixed(2)));
    console.log('   Displayed time:', displayedTime);

    // Test data persistence after simulated form interaction
    console.log('🔄 Testing data persistence during form interaction...');

    // Simulate some form interactions that might clear currentClickData
    window.markingTools.currentClickData = null; // Simulate data being cleared

    // Check if stored trade data is still available
    const persistedData = window.markingTools.currentTradeData;
    const dataPersisted = persistedData &&
                         persistedData.timestamp === currentTime &&
                         persistedData.price === testPrice;

    console.log('📊 Data Persistence Verification:');
    console.log('   Current click data cleared:', !window.markingTools.currentClickData);
    console.log('   Trade data persisted:', !!persistedData);
    console.log('   Persisted timestamp correct:', persistedData?.timestamp === currentTime);
    console.log('   Persisted price correct:', persistedData?.price === testPrice);

    console.log('✅ Exit modal data persistence test completed!');

    return {
        dataStoredCorrectly: dataStoredCorrectly,
        dataPersisted: dataPersisted,
        priceDisplayCorrect: displayedPrice?.includes(testPrice.toFixed(2)),
        storedData: storedTradeData,
        persistedData: persistedData
    };
};

// Test function for the new unified modal with dropdown approach
window.testUnifiedModalWithDropdown = async function() {
    console.log('🧪 Testing Unified Modal with Dropdown Approach...');

    if (!window.markingTools) {
        console.error('❌ Marking tools not available');
        return;
    }

    // Enable marking mode
    window.markingTools.enableMarkingMode();

    // Create realistic test click data with candlestick data
    const currentTime = Math.floor(Date.now() / 1000);
    const testPrice = 47890.12;

    window.markingTools.currentClickData = {
        time: currentTime,
        price: testPrice,
        x: 250,
        y: 180,
        candlestickData: {
            time: currentTime,
            open: 47800.50,
            high: 47950.75,
            low: 47750.25,
            close: testPrice,
            volume: 3456789
        }
    };

    console.log('📊 Test click data created:', window.markingTools.currentClickData);

    // Test 1: Open modal with entry preselected (left click simulation)
    console.log('🔍 Test 1: Opening modal with ENTRY preselected...');
    await window.markingTools.showTradeModal('entry');

    // Wait for modal to load
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Check dropdown value
    const tradeTypeSelect = document.getElementById('trade-type');
    const entryForm = document.getElementById('trade-entry-form');
    const exitForm = document.getElementById('trade-exit-form');
    const pnlSection = document.getElementById('trade-pnl-section');
    const confirmButton = document.getElementById('trade-confirm');

    console.log('📊 Entry Test Results:');
    console.log('   Dropdown value:', tradeTypeSelect?.value);
    console.log('   Entry form visible:', entryForm?.style.display !== 'none');
    console.log('   Exit form hidden:', exitForm?.style.display === 'none');
    console.log('   P&L section hidden:', pnlSection?.style.display === 'none');
    console.log('   Button text:', confirmButton?.textContent);

    // Test 2: Change dropdown to exit
    console.log('🔍 Test 2: Changing dropdown to EXIT...');
    if (tradeTypeSelect) {
        tradeTypeSelect.value = 'exit';
        tradeTypeSelect.dispatchEvent(new Event('change'));
    }

    // Wait for form to reconfigure
    await new Promise(resolve => setTimeout(resolve, 500));

    console.log('📊 Exit Test Results:');
    console.log('   Dropdown value:', tradeTypeSelect?.value);
    console.log('   Entry form visible:', entryForm?.style.display !== 'none');
    console.log('   Exit form visible:', exitForm?.style.display !== 'none');
    console.log('   P&L section visible:', pnlSection?.style.display !== 'none');
    console.log('   Button text:', confirmButton?.textContent);

    // Test 3: Check data persistence
    const storedTradeData = window.markingTools.currentTradeData;
    console.log('📊 Data Persistence Test:');
    console.log('   Trade data stored:', !!storedTradeData);
    console.log('   Timestamp correct:', storedTradeData?.timestamp === currentTime);
    console.log('   Price correct:', storedTradeData?.price === testPrice);
    console.log('   Real data displayed:', !document.getElementById('trade-price')?.textContent?.includes('$50000.00'));

    // Test 4: Close and reopen with exit preselected (right click simulation)
    console.log('🔍 Test 4: Closing and reopening with EXIT preselected...');
    window.markingTools.closeModal('trade-modal');

    await new Promise(resolve => setTimeout(resolve, 500));

    await window.markingTools.showTradeModal('exit');

    await new Promise(resolve => setTimeout(resolve, 1000));

    console.log('📊 Exit Preselection Test Results:');
    console.log('   Dropdown value:', tradeTypeSelect?.value);
    console.log('   Exit form visible:', exitForm?.style.display !== 'none');
    console.log('   P&L section visible:', pnlSection?.style.display !== 'none');
    console.log('   Button text:', confirmButton?.textContent);

    console.log('✅ Unified modal with dropdown test completed!');

    return {
        entryPreselectionWorks: tradeTypeSelect?.value === 'exit', // Should be exit from last test
        formConfigurationWorks: exitForm?.style.display !== 'none',
        dataStoredCorrectly: !!storedTradeData && storedTradeData.timestamp === currentTime,
        realDataDisplayed: !document.getElementById('trade-price')?.textContent?.includes('$50000.00')
    };
};

// Debug function to test right-click exit modal functionality
window.debugExitModalTrigger = function() {
    console.log('🔧 Debugging Exit Modal Trigger...');

    if (!window.markingTools) {
        console.error('❌ Marking tools not available');
        return;
    }

    // Check if marking mode is enabled
    console.log('📋 Marking mode status:', window.markingTools.isMarkingMode);

    if (!window.markingTools.isMarkingMode) {
        console.log('🔄 Enabling marking mode...');
        window.markingTools.enableMarkingMode();
    }

    // Check chart container and event listeners
    console.log('📊 Chart container:', !!window.markingTools.chart?.container);
    console.log('📊 Chart object:', !!window.markingTools.chart);

    // Simulate right-click by directly calling handleChartClick
    console.log('🖱️  Simulating right-click for exit modal...');

    // Create a mock event
    const mockEvent = {
        clientX: 300,
        clientY: 200,
        preventDefault: () => {}
    };

    // Set up mock chart container bounds
    if (window.markingTools.chart?.container) {
        const container = window.markingTools.chart.container;
        const originalGetBoundingClientRect = container.getBoundingClientRect;
        container.getBoundingClientRect = () => ({
            left: 100,
            top: 100,
            width: 800,
            height: 600
        });

        // Call the handler
        try {
            window.markingTools.handleChartClick(mockEvent, 'exit');
            console.log('✅ handleChartClick called successfully');
        } catch (error) {
            console.error('❌ Error in handleChartClick:', error);
        }

        // Restore original method
        container.getBoundingClientRect = originalGetBoundingClientRect;
    } else {
        console.error('❌ Chart container not available for simulation');
    }
};
