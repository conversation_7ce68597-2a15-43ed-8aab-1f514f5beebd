<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug API Response</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #131722;
            color: white;
            font-family: 'Segoe UI', sans-serif;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .section {
            margin-bottom: 30px;
            padding: 20px;
            background: rgba(38, 166, 154, 0.1);
            border-left: 3px solid #26a69a;
            border-radius: 4px;
        }
        
        .controls {
            margin: 20px 0;
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        
        input, select, button {
            padding: 8px 12px;
            background: #2a2e39;
            color: white;
            border: 1px solid #363c4e;
            border-radius: 4px;
        }
        
        button {
            background: #2962ff;
            cursor: pointer;
        }
        
        button:hover {
            background: #1e53e5;
        }
        
        .response {
            margin-top: 20px;
            padding: 15px;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        
        .success { color: #26a69a; }
        .error { color: #ef5350; }
        .warning { color: #ff9800; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Debug API Response</h1>
        
        <div class="section">
            <h2>Test OHLCV Data API</h2>
            <p>This tool helps debug the API response that should load into the chart.</p>
            
            <div class="controls">
                <input type="text" id="symbol" value="BTCUSDT" placeholder="Symbol">
                <select id="timeframe">
                    <option value="1m">1 Minute</option>
                    <option value="5m">5 Minutes</option>
                    <option value="15m" selected>15 Minutes</option>
                    <option value="1h">1 Hour</option>
                    <option value="4h">4 Hours</option>
                    <option value="1d">1 Day</option>
                </select>
                <input type="number" id="limit" value="100" placeholder="Limit" min="1" max="10000">
                <button id="test-api">Test API</button>
                <button id="clear-response">Clear</button>
            </div>
            
            <div class="response" id="api-response">
                <div class="warning">Click "Test API" to check the OHLCV data response...</div>
            </div>
        </div>
        
        <div class="section">
            <h2>Chart Loading Simulation</h2>
            <p>Simulate the exact same data processing that happens in strategy-manager.js</p>
            
            <div class="controls">
                <button id="simulate-processing" disabled>Simulate Data Processing</button>
                <button id="test-chart-update" disabled>Test Chart Update</button>
            </div>
            
            <div class="response" id="processing-response">
                <div class="warning">Load API data first to enable simulation...</div>
            </div>
        </div>
    </div>

    <script>
        let currentApiData = null;
        
        function log(message, type = 'info', containerId = 'api-response') {
            const container = document.getElementById(containerId);
            const timestamp = new Date().toLocaleTimeString();
            const colorClass = type === 'success' ? 'success' : type === 'error' ? 'error' : type === 'warning' ? 'warning' : '';
            
            const line = document.createElement('div');
            line.className = colorClass;
            line.textContent = `[${timestamp}] ${message}`;
            container.appendChild(line);
            container.scrollTop = container.scrollHeight;
            
            console.log(`[${type.toUpperCase()}] ${message}`);
        }
        
        function clearResponse(containerId) {
            document.getElementById(containerId).innerHTML = '';
        }
        
        async function testAPI() {
            const symbol = document.getElementById('symbol').value.trim();
            const timeframe = document.getElementById('timeframe').value;
            const limit = document.getElementById('limit').value;
            
            if (!symbol) {
                log('Please enter a symbol', 'error');
                return;
            }
            
            try {
                log('🔄 Testing API endpoint...');
                
                const params = new URLSearchParams({
                    symbol: symbol,
                    timeframe: timeframe,
                    limit: limit
                });
                
                const url = `/api/v1/ohlcv/data?${params}`;
                log(`📡 Requesting: ${url}`);
                
                const response = await fetch(url);
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const result = await response.json();
                
                log('✅ API Response received', 'success');
                log(`📊 Response structure:`, 'info');
                log(`  - success: ${result.success}`);
                log(`  - message: ${result.message}`);
                
                if (result.data) {
                    log(`  - data.count: ${result.data.count}`);
                    log(`  - data.symbol: ${result.data.symbol}`);
                    log(`  - data.timeframe: ${result.data.timeframe}`);
                    log(`  - data.ohlcv: ${result.data.ohlcv ? result.data.ohlcv.length : 'null'} items`);
                    
                    if (result.data.ohlcv && result.data.ohlcv.length > 0) {
                        log(`📈 Sample OHLCV item:`);
                        const sample = result.data.ohlcv[0];
                        log(`  ${JSON.stringify(sample, null, 2)}`);
                        
                        currentApiData = result.data.ohlcv;
                        document.getElementById('simulate-processing').disabled = false;
                        log('✅ Data loaded - simulation enabled', 'success');
                    } else {
                        log('⚠️ No OHLCV data in response', 'warning');
                        currentApiData = null;
                        document.getElementById('simulate-processing').disabled = true;
                    }
                } else {
                    log('❌ No data object in response', 'error');
                    currentApiData = null;
                }
                
                log('📋 Full response:');
                log(JSON.stringify(result, null, 2));
                
            } catch (error) {
                log(`❌ API Error: ${error.message}`, 'error');
                console.error('API Error:', error);
                currentApiData = null;
                document.getElementById('simulate-processing').disabled = true;
            }
        }
        
        function simulateDataProcessing() {
            if (!currentApiData) {
                log('❌ No API data available', 'error', 'processing-response');
                return;
            }
            
            clearResponse('processing-response');
            log('🔄 Simulating data processing...', 'info', 'processing-response');
            
            try {
                // Simulate the exact processing from strategy-manager.js
                log(`📊 Processing ${currentApiData.length} OHLCV items`, 'info', 'processing-response');
                log(`Sample input: ${JSON.stringify(currentApiData[0])}`, 'info', 'processing-response');
                
                // Process candle data
                const candleData = currentApiData.map(item => ({
                    time: Math.floor(new Date(item.timestamp).getTime() / 1000),
                    open: parseFloat(item.open),
                    high: parseFloat(item.high),
                    low: parseFloat(item.low),
                    close: parseFloat(item.close),
                })).sort((a, b) => a.time - b.time);
                
                log(`📈 Processed ${candleData.length} candle items`, 'success', 'processing-response');
                log(`Sample candle: ${JSON.stringify(candleData[0])}`, 'info', 'processing-response');
                
                // Process volume data
                const volumeData = currentApiData.map(item => ({
                    time: Math.floor(new Date(item.timestamp).getTime() / 1000),
                    value: parseFloat(item.volume),
                    color: parseFloat(item.close) >= parseFloat(item.open) ? '#26a69a80' : '#ef535080',
                })).sort((a, b) => a.time - b.time);
                
                log(`📊 Processed ${volumeData.length} volume items`, 'success', 'processing-response');
                log(`Sample volume: ${JSON.stringify(volumeData[0])}`, 'info', 'processing-response');
                
                // Validate data
                const validCandles = candleData.filter(c => 
                    !isNaN(c.time) && !isNaN(c.open) && !isNaN(c.high) && !isNaN(c.low) && !isNaN(c.close)
                );
                
                const validVolumes = volumeData.filter(v => 
                    !isNaN(v.time) && !isNaN(v.value)
                );
                
                log(`✅ Validation results:`, 'success', 'processing-response');
                log(`  - Valid candles: ${validCandles.length}/${candleData.length}`, 'info', 'processing-response');
                log(`  - Valid volumes: ${validVolumes.length}/${volumeData.length}`, 'info', 'processing-response');
                
                if (validCandles.length === candleData.length && validVolumes.length === volumeData.length) {
                    log('✅ All data is valid for chart loading', 'success', 'processing-response');
                    document.getElementById('test-chart-update').disabled = false;
                } else {
                    log('⚠️ Some data is invalid - this could cause chart loading issues', 'warning', 'processing-response');
                }
                
                // Check time range
                const timeRange = {
                    start: new Date(Math.min(...candleData.map(c => c.time)) * 1000),
                    end: new Date(Math.max(...candleData.map(c => c.time)) * 1000)
                };
                
                log(`📅 Time range: ${timeRange.start.toISOString()} to ${timeRange.end.toISOString()}`, 'info', 'processing-response');
                
                // Store processed data for chart test
                window.processedCandleData = validCandles;
                window.processedVolumeData = validVolumes;
                
            } catch (error) {
                log(`❌ Processing error: ${error.message}`, 'error', 'processing-response');
                log(`Stack trace: ${error.stack}`, 'error', 'processing-response');
            }
        }
        
        function testChartUpdate() {
            if (!window.processedCandleData || !window.processedVolumeData) {
                log('❌ No processed data available', 'error', 'processing-response');
                return;
            }
            
            log('🔄 Testing chart update simulation...', 'info', 'processing-response');
            
            try {
                // Simulate what happens in the actual chart update
                log('📊 Simulating chart.setData() calls...', 'info', 'processing-response');
                
                // Check if data would be accepted by TradingView
                const candleData = window.processedCandleData;
                const volumeData = window.processedVolumeData;
                
                // Validate TradingView data format
                const isValidCandleFormat = candleData.every(item => 
                    typeof item.time === 'number' &&
                    typeof item.open === 'number' &&
                    typeof item.high === 'number' &&
                    typeof item.low === 'number' &&
                    typeof item.close === 'number' &&
                    item.high >= Math.max(item.open, item.close) &&
                    item.low <= Math.min(item.open, item.close)
                );
                
                const isValidVolumeFormat = volumeData.every(item =>
                    typeof item.time === 'number' &&
                    typeof item.value === 'number' &&
                    item.value >= 0
                );
                
                log(`✅ TradingView format validation:`, 'success', 'processing-response');
                log(`  - Candle format valid: ${isValidCandleFormat}`, isValidCandleFormat ? 'success' : 'error', 'processing-response');
                log(`  - Volume format valid: ${isValidVolumeFormat}`, isValidVolumeFormat ? 'success' : 'error', 'processing-response');
                
                if (isValidCandleFormat && isValidVolumeFormat) {
                    log('🎉 Data should load successfully into TradingView chart!', 'success', 'processing-response');
                    log('💡 If chart still not updating, check:', 'warning', 'processing-response');
                    log('  1. Chart initialization timing', 'warning', 'processing-response');
                    log('  2. Series object availability', 'warning', 'processing-response');
                    log('  3. JavaScript errors in console', 'warning', 'processing-response');
                    log('  4. DOM element visibility', 'warning', 'processing-response');
                } else {
                    log('❌ Data format issues detected - this will prevent chart loading', 'error', 'processing-response');
                }
                
            } catch (error) {
                log(`❌ Chart update test error: ${error.message}`, 'error', 'processing-response');
            }
        }
        
        // Event listeners
        document.getElementById('test-api').addEventListener('click', testAPI);
        document.getElementById('clear-response').addEventListener('click', () => clearResponse('api-response'));
        document.getElementById('simulate-processing').addEventListener('click', simulateDataProcessing);
        document.getElementById('test-chart-update').addEventListener('click', testChartUpdate);
        
        log('🚀 API Debug Tool Ready', 'success');
    </script>
</body>
</html>
