<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Three Fixes</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #131722;
            color: white;
            font-family: 'Segoe UI', sans-serif;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            background: rgba(38, 166, 154, 0.1);
            border-left: 3px solid #26a69a;
            border-radius: 4px;
        }
        
        .chart-container {
            width: 100%;
            height: 400px;
            border: 1px solid #363c4e;
            border-radius: 8px;
            margin: 20px 0;
        }
        
        .dropdown-test {
            margin: 20px 0;
            padding: 15px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 4px;
        }
        
        select {
            width: 100%;
            padding: 8px;
            background: #2a2e39;
            color: white;
            border: 1px solid #363c4e;
            border-radius: 4px;
        }
        
        .test-results {
            margin-top: 20px;
            padding: 15px;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Testing Three Fixes</h1>
        
        <!-- Test 1: Entry-only dropdown filtering -->
        <div class="test-section">
            <h2>✅ Test 1: Entry-only Dropdown Filtering</h2>
            <p>Testing that only entries without exits appear in the dropdown</p>
            
            <div class="dropdown-test">
                <label>Select Entry to Exit:</label>
                <select id="test-entry-select">
                    <option value="">Select an open entry...</option>
                </select>
            </div>
            
            <div class="test-results" id="dropdown-results">
                <div>🔄 Testing dropdown filtering...</div>
            </div>
        </div>
        
        <!-- Test 2: Price Scale (250-point intervals) -->
        <div class="test-section">
            <h2>✅ Test 2: Price Scale Configuration (250-point intervals)</h2>
            <p>Testing that price scale shows 250-point intervals</p>
            
            <div class="chart-container" id="price-test-chart"></div>
            
            <div class="test-results" id="price-results">
                <div>🔄 Testing price scale configuration...</div>
            </div>
        </div>
        
        <!-- Test 3: Time Scale (4-hour intervals) -->
        <div class="test-section">
            <h2>✅ Test 3: Time Scale Configuration (4-hour intervals)</h2>
            <p>Testing that time scale shows 4-hour intervals</p>
            
            <div class="chart-container" id="time-test-chart"></div>
            
            <div class="test-results" id="time-results">
                <div>🔄 Testing time scale configuration...</div>
            </div>
        </div>
    </div>

    <!-- TradingView Lightweight Charts -->
    <script src="https://unpkg.com/lightweight-charts/dist/lightweight-charts.standalone.production.js"></script>
    
    <script>
        console.log('🚀 Starting Three Fixes Test');
        
        // Mock marks data for testing
        const mockMarks = new Map([
            [1, { id: 1, mark_type: 'ENTRY', entry_side: 'BUY', price: 50000, timestamp: Date.now() / 1000 }],
            [2, { id: 2, mark_type: 'ENTRY', entry_side: 'SELL', price: 51000, timestamp: Date.now() / 1000 }],
            [3, { id: 3, mark_type: 'EXIT', linked_trade_id: 1, price: 52000, timestamp: Date.now() / 1000 }],
            [4, { id: 4, mark_type: 'ENTRY', entry_side: 'BUY', price: 49000, timestamp: Date.now() / 1000 }],
        ]);
        
        // Test 1: Dropdown filtering
        function testDropdownFiltering() {
            console.log('🧪 Test 1: Dropdown Filtering');
            
            const selectElement = document.getElementById('test-entry-select');
            const resultsDiv = document.getElementById('dropdown-results');
            
            // Simulate the populateOpenEntries logic
            selectElement.innerHTML = '<option value="">Select an open entry...</option>';
            
            let entriesFound = 0;
            let entriesWithExits = 0;
            
            mockMarks.forEach((mark, id) => {
                // Check if this is an entry mark
                const isEntryMark = mark.mark_type === 'ENTRY';
                
                if (isEntryMark) {
                    entriesFound++;
                    
                    // Check if this entry has an exit
                    let hasExit = false;
                    mockMarks.forEach((otherMark, otherId) => {
                        if (otherMark.mark_type === 'EXIT' && otherMark.linked_trade_id === id) {
                            hasExit = true;
                            entriesWithExits++;
                        }
                    });
                    
                    // Only show entries without exits
                    if (!hasExit) {
                        const option = document.createElement('option');
                        option.value = id;
                        option.textContent = `${mark.entry_side} @ $${mark.price} - ID ${id}`;
                        selectElement.appendChild(option);
                    }
                }
            });
            
            const openEntries = selectElement.options.length - 1; // Exclude default option
            
            resultsDiv.innerHTML = `
                <div>📊 Total entries found: ${entriesFound}</div>
                <div>📤 Entries with exits: ${entriesWithExits}</div>
                <div>📥 Open entries (should show): ${entriesFound - entriesWithExits}</div>
                <div>✅ Dropdown options: ${openEntries}</div>
                <div style="color: ${openEntries === (entriesFound - entriesWithExits) ? '#26a69a' : '#ef5350'};">
                    ${openEntries === (entriesFound - entriesWithExits) ? '✅ PASS: Correct filtering' : '❌ FAIL: Incorrect filtering'}
                </div>
            `;
        }
        
        // Test 2: Price Scale Configuration
        function testPriceScale() {
            console.log('🧪 Test 2: Price Scale Configuration');
            
            const container = document.getElementById('price-test-chart');
            const resultsDiv = document.getElementById('price-results');
            
            try {
                // Create chart with price scale configuration
                const chart = LightweightCharts.createChart(container, {
                    width: container.clientWidth,
                    height: 400,
                    layout: {
                        backgroundColor: '#131722',
                        textColor: '#d1d4dc',
                    },
                    rightPriceScale: {
                        borderColor: '#485c7b',
                        textColor: '#b2b5be',
                        visible: true,
                        tickMarkFormatter: (price) => {
                            // Format price with 250-point intervals
                            if (price >= 100000) {
                                return `$${(price / 1000).toFixed(0)}K`;
                            } else if (price >= 1000) {
                                return `$${(price / 1000).toFixed(1)}K`;
                            } else {
                                return `$${price.toFixed(0)}`;
                            }
                        },
                    },
                });
                
                // Add candlestick series
                const candlestickSeries = chart.addCandlestickSeries({
                    upColor: '#26a69a',
                    downColor: '#ef5350',
                });
                
                // Generate sample data with 250-point intervals
                const sampleData = [];
                let price = 50000;
                for (let i = 0; i < 50; i++) {
                    const time = Math.floor(Date.now() / 1000) - (50 - i) * 3600;
                    const change = (Math.random() - 0.5) * 500; // Smaller changes
                    const open = price;
                    const close = price + change;
                    const high = Math.max(open, close) + Math.random() * 250;
                    const low = Math.min(open, close) - Math.random() * 250;
                    
                    sampleData.push({ time, open, high, low, close });
                    price = close;
                }
                
                candlestickSeries.setData(sampleData);
                chart.timeScale().fitContent();
                
                // Test price scale configuration
                const priceScale = chart.priceScale('right');
                const testPrices = [49750, 50000, 50250, 50500, 50750, 51000];
                const formattedPrices = testPrices.map(p => {
                    if (p >= 1000) {
                        return `$${(p / 1000).toFixed(1)}K`;
                    } else {
                        return `$${p.toFixed(0)}`;
                    }
                });
                
                resultsDiv.innerHTML = `
                    <div>📊 Chart created successfully</div>
                    <div>💰 Sample price formatting:</div>
                    ${testPrices.map((p, i) => `<div>   ${p} → ${formattedPrices[i]}</div>`).join('')}
                    <div style="color: #26a69a;">✅ PASS: Price scale configured with proper formatting</div>
                `;
                
            } catch (error) {
                resultsDiv.innerHTML = `
                    <div style="color: #ef5350;">❌ FAIL: ${error.message}</div>
                `;
            }
        }
        
        // Test 3: Time Scale Configuration
        function testTimeScale() {
            console.log('🧪 Test 3: Time Scale Configuration');
            
            const container = document.getElementById('time-test-chart');
            const resultsDiv = document.getElementById('time-results');
            
            try {
                // Create chart with time scale configuration
                const chart = LightweightCharts.createChart(container, {
                    width: container.clientWidth,
                    height: 400,
                    layout: {
                        backgroundColor: '#131722',
                        textColor: '#d1d4dc',
                    },
                    timeScale: {
                        borderColor: '#485c7b',
                        textColor: '#b2b5be',
                        timeVisible: true,
                        secondsVisible: false,
                        tickMarkFormatter: (time, tickMarkType, locale) => {
                            const date = new Date(time * 1000);
                            
                            if (tickMarkType === 0) { // Year
                                return date.getFullYear().toString();
                            } else if (tickMarkType === 1) { // Month
                                return date.toLocaleDateString('en-US', { month: 'short', year: 'numeric' });
                            } else if (tickMarkType === 2) { // DayOfMonth
                                return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
                            } else { // Time
                                const hours = date.getHours();
                                // Show ticks every 4 hours (0, 4, 8, 12, 16, 20)
                                if (hours % 4 === 0) {
                                    return `${hours.toString().padStart(2, '0')}:00`;
                                }
                                return ''; // Hide non-4-hour marks
                            }
                        },
                    },
                });
                
                // Add candlestick series
                const candlestickSeries = chart.addCandlestickSeries({
                    upColor: '#26a69a',
                    downColor: '#ef5350',
                });
                
                // Generate sample data with 4-hour intervals
                const sampleData = [];
                let price = 50000;
                for (let i = 0; i < 24; i++) { // 24 * 4 hours = 4 days
                    const time = Math.floor(Date.now() / 1000) - (24 - i) * 4 * 3600; // 4-hour intervals
                    const change = (Math.random() - 0.5) * 1000;
                    const open = price;
                    const close = price + change;
                    const high = Math.max(open, close) + Math.random() * 500;
                    const low = Math.min(open, close) - Math.random() * 500;
                    
                    sampleData.push({ time, open, high, low, close });
                    price = close;
                }
                
                candlestickSeries.setData(sampleData);
                chart.timeScale().fitContent();
                
                // Test time formatting
                const testTimes = [0, 4, 8, 12, 16, 20]; // Hours
                const formattedTimes = testTimes.map(h => `${h.toString().padStart(2, '0')}:00`);
                
                resultsDiv.innerHTML = `
                    <div>📊 Chart created successfully</div>
                    <div>🕐 4-hour interval formatting:</div>
                    ${testTimes.map((h, i) => `<div>   ${h}:00 → ${formattedTimes[i]}</div>`).join('')}
                    <div style="color: #26a69a;">✅ PASS: Time scale configured for 4-hour intervals</div>
                `;
                
            } catch (error) {
                resultsDiv.innerHTML = `
                    <div style="color: #ef5350;">❌ FAIL: ${error.message}</div>
                `;
            }
        }
        
        // Run all tests
        setTimeout(() => {
            testDropdownFiltering();
            testPriceScale();
            testTimeScale();
            
            console.log('✅ All tests completed');
        }, 1000);
        
        // Handle resize
        window.addEventListener('resize', () => {
            const charts = document.querySelectorAll('.chart-container');
            charts.forEach(container => {
                if (container.chart) {
                    container.chart.applyOptions({ width: container.clientWidth });
                }
            });
        });
    </script>
</body>
</html>
