// Debug script to test tooltip functionality
console.log('🔧 Debug Tooltip Script Loaded');

// Function to test tooltip functionality
function debugTooltip() {
    console.log('=== TOOLTIP DEBUG ===');
    
    // Check if chart exists
    console.log('1. Chart availability:');
    console.log('   - window.professionalChart:', !!window.professionalChart);
    console.log('   - window.tradingViewChart:', !!window.tradingViewChart);
    
    const chart = window.professionalChart || window.tradingViewChart;
    if (!chart) {
        console.log('❌ No chart found');
        return;
    }
    
    console.log('✅ Chart found:', chart.constructor.name);
    
    // Check tooltip elements
    console.log('2. Tooltip elements:');
    console.log('   - chart.ohlcvTooltip:', !!chart.ohlcvTooltip);
    console.log('   - chart.priceDisplay:', !!chart.priceDisplay);
    
    // Check series
    console.log('3. Chart series:');
    console.log('   - candlestickSeries:', !!chart.candlestickSeries);
    console.log('   - volumeSeries:', !!chart.volumeSeries);
    
    // Check indicators
    console.log('4. Indicators:');
    console.log('   - window.indicatorsManager:', !!window.indicatorsManager);
    if (window.indicatorsManager) {
        console.log('   - indicatorData:', !!window.indicatorsManager.indicatorData);
        console.log('   - getIndicatorDataByTimestamp method:', typeof window.indicatorsManager.getIndicatorDataByTimestamp);
        
        if (window.indicatorsManager.indicatorData) {
            console.log('   - Available indicators:', Object.keys(window.indicatorsManager.indicatorData));
        }
    }
    
    // Test tooltip creation
    console.log('5. Testing tooltip creation:');
    if (chart.createOHLCVTooltip) {
        try {
            chart.createOHLCVTooltip();
            console.log('✅ Tooltip creation successful');
        } catch (error) {
            console.log('❌ Tooltip creation failed:', error);
        }
    }
    
    // Check for competing tooltips
    console.log('6. Checking for competing tooltips:');
    const indicatorTooltip = document.getElementById('indicator-tooltip');
    console.log('   - indicator-tooltip element:', !!indicatorTooltip);
    if (indicatorTooltip) {
        console.log('   - indicator-tooltip display:', indicatorTooltip.style.display);
        console.log('   - Removing old indicator tooltip...');
        indicatorTooltip.remove();
    }
    
    const ohlcvTooltips = document.querySelectorAll('.ohlcv-tooltip');
    console.log('   - ohlcv-tooltip elements:', ohlcvTooltips.length);
    
    // Test crosshair subscription
    console.log('7. Testing crosshair subscription:');
    if (chart.chart && chart.chart.subscribeCrosshairMove) {
        console.log('✅ Crosshair subscription available');
        
        // Test with fake data
        const testParam = {
            time: Math.floor(Date.now() / 1000),
            point: { x: 100, y: 100 },
            seriesData: new Map()
        };
        
        // Add fake candle data
        if (chart.candlestickSeries) {
            testParam.seriesData.set(chart.candlestickSeries, {
                open: 50000,
                high: 51000,
                low: 49000,
                close: 50500
            });
        }
        
        // Add fake volume data
        if (chart.volumeSeries) {
            testParam.seriesData.set(chart.volumeSeries, {
                value: 1000000
            });
        }
        
        console.log('   - Test param:', testParam);
        
        try {
            if (chart.showOHLCVTooltip) {
                chart.showOHLCVTooltip(testParam);
                console.log('✅ showOHLCVTooltip called successfully');
            }
        } catch (error) {
            console.log('❌ showOHLCVTooltip failed:', error);
        }
    }
    
    console.log('=== DEBUG COMPLETE ===');
}

// Auto-run debug after a delay
setTimeout(() => {
    debugTooltip();
}, 2000);

// Function to manually test tooltip
function testTooltipManually() {
    console.log('🧪 Manual Tooltip Test');

    const chart = window.professionalChart || window.tradingViewChart;
    if (!chart) {
        console.log('❌ No chart found');
        return;
    }

    // Create test parameters
    const testParam = {
        time: Math.floor(Date.now() / 1000) - 3600, // 1 hour ago
        point: { x: 200, y: 200 },
        seriesData: new Map()
    };

    // Add realistic candle data
    if (chart.candlestickSeries) {
        testParam.seriesData.set(chart.candlestickSeries, {
            open: 118000.50,
            high: 119500.75,
            low: 117200.25,
            close: 118750.00
        });
    }

    // Add realistic volume data
    if (chart.volumeSeries) {
        testParam.seriesData.set(chart.volumeSeries, {
            value: 1250000
        });
    }

    console.log('📊 Test parameters:', testParam);

    // Call the tooltip function directly
    if (chart.showOHLCVTooltip) {
        try {
            chart.showOHLCVTooltip(testParam);
            console.log('✅ Manual tooltip test successful');

            // Check if tooltip is visible
            setTimeout(() => {
                if (chart.ohlcvTooltip) {
                    console.log('📍 Tooltip visibility:', chart.ohlcvTooltip.style.display);
                    console.log('📍 Tooltip content length:', chart.ohlcvTooltip.innerHTML.length);
                    console.log('📍 Tooltip position:', {
                        left: chart.ohlcvTooltip.style.left,
                        top: chart.ohlcvTooltip.style.top
                    });
                }
            }, 100);

        } catch (error) {
            console.log('❌ Manual tooltip test failed:', error);
        }
    }
}

// Make functions available globally
window.debugTooltip = debugTooltip;
window.testTooltipManually = testTooltipManually;

console.log('🔧 Debug functions available: window.debugTooltip(), window.testTooltipManually()');
